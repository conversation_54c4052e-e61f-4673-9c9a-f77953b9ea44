# PyIDE Ignore Patterns
# This file controls which files and directories the AI can access
# Uses gitignore syntax

# Python cache and build files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and temporary files
*.log
*.tmp
*.temp
.cache/

# Sensitive files
*.key
*.pem
*.p12
*.pfx
config.local.json
.env.local
secrets.json

# Large binary files
*.zip
*.tar.gz
*.rar
*.7z
*.exe
*.dll
*.so
*.dylib

# Media files
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.ico
*.mp3
*.mp4
*.avi
*.mov
*.wav

# Database files
*.db
*.sqlite
*.sqlite3

# Node.js (if applicable)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Git
.git/
.gitignore

# Documentation build
docs/_build/
site/

# Test coverage
.coverage
htmlcov/
.pytest_cache/

# Jupyter Notebook checkpoints
.ipynb_checkpoints/
