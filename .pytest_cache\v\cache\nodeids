["tests/test_orchestration.py::TestErrorHandler::test_error_classification", "tests/test_orchestration.py::TestErrorHandler::test_retry_config", "tests/test_orchestration.py::TestErrorHandler::test_retry_logic", "tests/test_orchestration.py::TestExecutionPlan::test_circular_dependency_detection", "tests/test_orchestration.py::TestExecutionPlan::test_dependency_management", "tests/test_orchestration.py::TestExecutionPlan::test_parallel_groups", "tests/test_orchestration.py::TestExecutionPlan::test_plan_creation", "tests/test_orchestration.py::TestExecutionPlan::test_plan_validation", "tests/test_orchestration.py::TestExecutionPlan::test_step_addition", "tests/test_orchestration.py::TestExecutionState::test_dependency_tracking", "tests/test_orchestration.py::TestExecutionState::test_state_creation", "tests/test_orchestration.py::TestExecutionState::test_state_transitions", "tests/test_orchestration.py::TestProgressReporter::test_event_handling", "tests/test_orchestration.py::TestProgressReporter::test_progress_tracking", "tests/test_orchestration.py::TestRequestAnalyzer::test_execution_plan_creation", "tests/test_orchestration.py::TestRequestAnalyzer::test_multi_step_detection", "tests/test_orchestration.py::TestRequestAnalyzer::test_project_analysis_pattern", "tests/test_orchestration.py::TestRequestAnalyzer::test_single_operation_detection", "tests/test_orchestration.py::TestToolOrchestrator::test_error_handling", "tests/test_orchestration.py::TestToolOrchestrator::test_multi_step_execution", "tests/test_orchestration.py::TestToolOrchestrator::test_parallel_execution", "tests/test_orchestration.py::TestToolOrchestrator::test_single_tool_execution"]