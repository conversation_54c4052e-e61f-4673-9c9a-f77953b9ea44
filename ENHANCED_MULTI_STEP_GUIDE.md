# Enhanced Multi-Step Execution Guide

## Overview

Your PyIDE project has been significantly enhanced with advanced multi-step execution capabilities. The system can now intelligently detect, plan, and execute complex operations that require multiple tool calls in sequence or parallel.

## Key Enhancements

### 1. Enhanced Request Analysis

The `RequestAnalyzer` now includes:

- **Numerical Indicators**: Detects requests like "create 3 files" or "read 5 scripts"
- **Multiple Action Detection**: Identifies multiple verbs in requests (read, create, modify, etc.)
- **Implicit Multi-Step Detection**: Recognizes complex operations like "refactor project"
- **Context-Aware Analysis**: Better understanding of project-wide operations

**Example Requests That Trigger Multi-Step:**
```
- "create 3 Python files for a web application"
- "read all files and then analyze them"
- "search for TODO comments and create a task list"
- "refactor the entire project structure"
- "analyze the workspace and generate documentation"
```

### 2. Dynamic Plan Generation

The new `DynamicPlanner` provides:

- **Adaptive Planning**: Plans that modify themselves based on intermediate results
- **Result-Based Step Generation**: Automatically creates new steps based on tool outputs
- **Conditional Operations**: Steps that execute based on file content or conditions
- **Template-Based Planning**: Reusable plan templates for common operations

**Example Dynamic Adaptations:**
- `list_files` result automatically generates `read_file` steps for discovered files
- `search_files` result creates `read_file` steps for matching files
- Python files with `if __name__ == '__main__':` automatically get execution steps
- Files with TODO comments trigger task list creation

### 3. Improved Error Recovery

Enhanced error handling includes:

- **Recoverable Error Detection**: Identifies errors that can be automatically fixed
- **Missing File Recovery**: Auto-creates missing files when appropriate
- **Alternative Step Execution**: Continues with similar tools when one fails
- **Optional Step Support**: Steps marked as optional don't stop execution on failure

### 4. Enhanced Progress Reporting

Better visibility into multi-step operations:

- **Detailed Progress**: Shows current step, estimated time, and success rate
- **Step Duration Tracking**: Monitors how long each step takes
- **Failure Analysis**: Reports which tools failed and why
- **Real-time Updates**: Live progress updates during execution

### 5. Intelligent Batching

Optimized execution through:

- **Similar Operation Batching**: Groups similar operations for efficiency
- **Parallel Execution**: Runs independent operations simultaneously
- **Batch Size Limits**: Prevents overwhelming the system with too many parallel operations
- **Timeout Management**: Handles batch operations with appropriate timeouts

## Configuration Options

The enhanced system is highly configurable through `config.json`:

```json
{
  "orchestration": {
    "enabled": true,
    "enable_dynamic_planning": true,
    "multi_step_detection": {
      "enabled": true,
      "confidence_threshold": 0.5,
      "max_auto_steps": 20,
      "enable_implicit_detection": true
    },
    "error_recovery": {
      "enabled": true,
      "auto_create_missing_files": true,
      "skip_permission_errors": true,
      "max_recovery_attempts": 3
    },
    "batching": {
      "enabled": true,
      "max_batch_size": 10,
      "batch_similar_operations": true,
      "parallel_batch_execution": true
    }
  }
}
```

## Usage Examples

### Example 1: Creating Multiple Files

**Input:** "create 3 Python files for a calculator project"

**What Happens:**
1. Request analyzer detects numerical indicator (3 files)
2. Creates execution plan with 3 `write_to_file` steps
3. Executes steps in parallel for efficiency
4. Reports progress and completion

### Example 2: Project Analysis

**Input:** "analyze all Python files in the workspace"

**What Happens:**
1. Detects project-wide operation
2. Creates plan: `list_files` → multiple `read_file` steps
3. `list_files` discovers Python files
4. Dynamic planner auto-generates `read_file` steps for each file
5. Executes reads in parallel
6. Reports analysis completion with success rate

### Example 3: Search and Process

**Input:** "find all TODO comments and create a summary"

**What Happens:**
1. Creates plan: `search_files` → `read_file` → `write_to_file`
2. Searches for TODO patterns
3. Dynamic planner creates read steps for matching files
4. Analyzes content and generates summary file
5. Reports completion with file count and summary location

## Advanced Features

### Custom Result Handlers

You can register custom handlers for specific tool results:

```python
def custom_handler(plan, step, result):
    # Custom logic based on result
    modifications = []
    # ... create modifications
    return modifications

orchestrator.dynamic_planner.register_result_handler("my_tool", custom_handler)
```

### Condition Evaluators

Register custom conditions for dynamic planning:

```python
def custom_condition(plan, context):
    # Evaluate condition and return modifications
    return modifications

orchestrator.dynamic_planner.register_condition_evaluator("my_condition", custom_condition)
```

### Plan Templates

Create reusable plan templates:

```python
template_plan = ExecutionPlan(name="Web Project Setup")
# ... add steps to template
orchestrator.dynamic_planner.register_plan_template("web_setup", template_plan)
```

## Monitoring and Debugging

### Progress Monitoring

The system provides detailed progress information:

```python
progress = orchestrator.progress_reporter.get_progress_summary()
print(f"Progress: {progress['overall_progress']:.1f}%")
print(f"Success Rate: {progress['success_rate']:.1f}%")
print(f"Active Tools: {progress['active_tools']}")
```

### Execution Status

Check current execution status:

```python
status = orchestrator.get_execution_status()
print(f"Is Executing: {status['is_executing']}")
print(f"Current Plan: {status['current_plan']}")
```

### Error Analysis

Review error patterns:

```python
error_summary = orchestrator.error_handler.get_error_summary()
print(f"Common Errors: {error_summary['common_patterns']}")
```

## Best Practices

1. **Use Descriptive Requests**: Clear, specific requests get better multi-step detection
2. **Enable Progress Reporting**: Monitor complex operations for better user experience
3. **Configure Error Recovery**: Set appropriate recovery options for your use case
4. **Test with Batching**: Enable batching for operations on multiple files
5. **Monitor Performance**: Use progress summaries to optimize execution plans

## Troubleshooting

### Common Issues

1. **Steps Not Executing in Parallel**: Check `max_parallel_executions` setting
2. **Dynamic Planning Not Working**: Ensure `enable_dynamic_planning` is true
3. **Error Recovery Too Aggressive**: Adjust `error_recovery` settings
4. **Batch Operations Timing Out**: Increase `batch_timeout` value

### Debug Mode

Enable detailed logging for troubleshooting:

```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

## Performance Considerations

- **Parallel Execution**: Improves performance but uses more resources
- **Batch Size**: Larger batches are faster but may hit resource limits
- **Dynamic Planning**: Adds intelligence but slight overhead
- **Progress Reporting**: Detailed reporting has minimal performance impact

## Future Enhancements

The system is designed for extensibility. Future enhancements may include:

- Machine learning-based request analysis
- More sophisticated error recovery strategies
- Integration with external tools and APIs
- Advanced workflow templates
- Performance optimization based on usage patterns

## Conclusion

The enhanced multi-step execution system transforms PyIDE from a simple tool executor into an intelligent assistant capable of understanding complex requests and executing sophisticated workflows. The system adapts to your needs while providing transparency and control over the execution process.
