# Multi-Step Tool Execution Guide

This guide explains PyIDE's intelligent multi-step tool execution system, which automatically orchestrates complex operations by breaking them down into sequential and parallel tool calls.

## Overview

The multi-step tool execution system provides:

- **Intelligent Request Analysis**: Automatically detects when user requests require multiple tool calls
- **Execution Planning**: Creates optimized execution plans with dependency management
- **Parallel Execution**: Runs independent operations in parallel for better performance
- **Error Handling**: Robust retry logic with exponential backoff
- **Progress Reporting**: Real-time progress updates for long-running operations
- **Batch Optimization**: Groups similar operations for efficiency

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Multi-Step Tool Execution                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Request Analyzer│  │ Tool Orchestrator│  │ Progress     │ │
│  │ - Parse requests│  │ - Plan sequences │  │ Reporter     │ │
│  │ - Detect multi- │  │ - Manage deps    │  │ - Real-time  │ │
│  │   step needs    │  │ - Execute tools  │  │   updates    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│           │                     │                    │      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Execution State │  │ E<PERSON>r Handler   │  │ Batch        │ │
│  │ Manager         │  │ - Retry logic   │  │ Optimizer    │ │
│  │ - Track status  │  │ - Exponential   │  │ - Group ops  │ │
│  │ - Store results │  │   backoff       │  │ - Efficiency │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Configuration

### Basic Configuration

Add the following to your `config.json`:

```json
{
    "orchestration": {
        "enabled": true,
        "max_parallel_executions": 5,
        "default_timeout": 60.0,
        "enable_optimization": true,
        "continue_on_failure": false,
        "progress_reporting": {
            "enabled": true,
            "show_detailed_progress": true,
            "show_estimated_time": true
        },
        "retry_config": {
            "max_attempts": 3,
            "base_delay": 1.0,
            "max_delay": 60.0,
            "backoff_multiplier": 2.0,
            "jitter": true
        }
    }
}
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enabled` | boolean | `true` | Enable/disable multi-step orchestration |
| `max_parallel_executions` | integer | `5` | Maximum number of parallel tool executions |
| `default_timeout` | float | `60.0` | Default timeout for tool executions (seconds) |
| `enable_optimization` | boolean | `true` | Enable execution plan optimization |
| `continue_on_failure` | boolean | `false` | Continue execution even if some steps fail |

#### Progress Reporting Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enabled` | boolean | `true` | Enable progress reporting |
| `show_detailed_progress` | boolean | `true` | Show detailed progress information |
| `show_estimated_time` | boolean | `true` | Show estimated completion times |

#### Retry Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `max_attempts` | integer | `3` | Maximum retry attempts |
| `base_delay` | float | `1.0` | Base delay between retries (seconds) |
| `max_delay` | float | `60.0` | Maximum delay between retries (seconds) |
| `backoff_multiplier` | float | `2.0` | Exponential backoff multiplier |
| `jitter` | boolean | `true` | Add random jitter to prevent thundering herd |

## Request Patterns

The system automatically recognizes these multi-step request patterns:

### Project Analysis
**Triggers**: "check project files", "analyze workspace", "review all files"

**Execution Plan**:
1. `list_files` - Discover all files in workspace
2. `read_file` (parallel) - Read each discovered file

**Example**:
```
User: "Check all my project files"
→ Lists files in workspace
→ Reads each file in parallel
→ Provides comprehensive analysis
```

### Multi-File Operations
**Triggers**: "read all files", "show everything", "display entire project"

**Execution Plan**:
1. `list_files` - Get file list
2. `read_file` (parallel) - Read all files simultaneously

### Search and Process
**Triggers**: "search for X and analyze", "find pattern and process"

**Execution Plan**:
1. `search_files` - Find files matching pattern
2. `read_file` (parallel) - Read matching files

### Project Creation
**Triggers**: "create project structure", "setup new project", "initialize workspace"

**Execution Plan**:
1. `create_directory` (sequential) - Create directory structure
2. `write_to_file` (sequential) - Create project files

## Execution Modes

### Sequential Execution
Steps execute one after another, respecting dependencies.

```python
step1 = ExecutionStep("step1", "list_files", {"path": "."}, set())
step2 = ExecutionStep("step2", "read_file", {"path": "main.py"}, {"step1"})
```

### Parallel Execution
Independent steps execute simultaneously for better performance.

```python
step1 = ExecutionStep("step1", "read_file", {"path": "file1.py"}, set(), mode=ExecutionMode.PARALLEL)
step2 = ExecutionStep("step2", "read_file", {"path": "file2.py"}, set(), mode=ExecutionMode.PARALLEL)
```

### Conditional Execution
Steps execute based on conditions or previous results.

```python
step = ExecutionStep("step1", "write_file", params, set(), mode=ExecutionMode.CONDITIONAL)
```

## Error Handling

### Error Classification

The system automatically classifies errors:

- **Network Errors**: Connection issues, timeouts
- **File Errors**: File not found, permission denied
- **Validation Errors**: Invalid parameters, malformed data
- **Resource Errors**: Memory, disk space issues

### Retry Strategies

#### Exponential Backoff (Default)
```
Attempt 1: 1.0s delay
Attempt 2: 2.0s delay  
Attempt 3: 4.0s delay
```

#### Linear Backoff
```
Attempt 1: 1.0s delay
Attempt 2: 2.0s delay
Attempt 3: 3.0s delay
```

#### Fixed Delay
```
All attempts: 1.0s delay
```

### Custom Retry Configuration

```python
from core.orchestration import RetryConfig, RetryStrategy

# Configure retry for specific tools
config = RetryConfig(
    strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
    max_attempts=5,
    base_delay=0.5,
    max_delay=30.0
)

orchestrator.set_retry_config("read_file", config)
```

## Progress Reporting

### Real-time Updates

The system provides real-time progress updates:

```
🔄 Starting execution plan with 5 steps
📁 Step 1/5: Listing files... (0%)
📖 Step 2/5: Reading main.py... (25%)
📖 Step 3/5: Reading utils.py... (50%)
📖 Step 4/5: Reading config.py... (75%)
✅ Step 5/5: Analysis complete (100%)
🎉 Plan completed in 2.3s
```

### Progress Events

You can listen to progress events:

```python
def progress_handler(event):
    print(f"{event.tool_name}: {event.message} ({event.progress_percent}%)")

orchestrator.add_progress_handler(progress_handler)
```

## Batch Optimization

### Automatic Batching

The system automatically groups similar operations:

#### Read Operations
```python
# These steps will be batched and executed in parallel
read_file("file1.py")
read_file("file2.py") 
read_file("file3.py")
```

#### Directory Creation
```python
# Directories are created in dependency order
create_directory("src")
create_directory("src/utils")  # Depends on "src"
create_directory("tests")      # Independent, runs in parallel
```

#### Search Operations
```python
# Different search patterns run in parallel
search_files(".", "*.py")
search_files(".", "*.json")
```

### Manual Batch Configuration

```python
# Add batch tags to steps
step.add_tag("batch_read")
step.add_tag("batch_write")
```

## Usage Examples

### Basic Multi-Step Execution

```python
from core.orchestration import ToolOrchestrator

orchestrator = ToolOrchestrator()

# Register tool executors
orchestrator.register_tool_executor("read_file", my_read_function)
orchestrator.register_tool_executor("list_files", my_list_function)

# Analyze and execute user request
result = await orchestrator.analyze_and_execute("check all project files")

if result["success"]:
    print("Multi-step operation completed successfully!")
else:
    print(f"Operation failed: {result['error']}")
```

### Custom Execution Plan

```python
from core.orchestration import ExecutionPlan, ExecutionStep

# Create custom plan
plan = ExecutionPlan(name="Custom Analysis")

# Add steps
step1 = ExecutionStep("list", "list_files", {"path": "."}, set())
step2 = ExecutionStep("read", "read_file", {"path": "main.py"}, {"list"})

plan.add_step(step1)
plan.add_step(step2)

# Execute plan
result = await orchestrator.execute_plan(plan)
```

### Error Handling Example

```python
# Configure retry behavior
from core.orchestration import RetryConfig, RetryStrategy

retry_config = RetryConfig(
    strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
    max_attempts=5,
    base_delay=1.0
)

orchestrator.set_retry_config("network_operation", retry_config)

# Execute with automatic retry
result = await orchestrator.analyze_and_execute("fetch remote data")
```

## Performance Optimization

### Parallel Execution Tips

1. **Independent Operations**: Mark operations as parallel when they don't depend on each other
2. **Resource Limits**: Configure `max_parallel_executions` based on system resources
3. **Batch Similar Operations**: Group similar operations for better efficiency

### Memory Management

1. **Large Files**: Use streaming for large file operations
2. **Result Caching**: Cache intermediate results when possible
3. **Cleanup**: Clear execution state after completion

### Monitoring

```python
# Get execution status
status = orchestrator.get_execution_status()
print(f"Active executions: {status['progress']['active_executions']}")
print(f"Failed executions: {status['progress']['failed_executions']}")

# Get error summary
errors = orchestrator.error_handler.get_error_summary()
print(f"Total errors: {errors['total_errors']}")
```

## Troubleshooting

### Common Issues

#### Execution Hangs
- Check for circular dependencies in execution plan
- Verify tool executors are properly registered
- Check timeout configurations

#### High Memory Usage
- Reduce `max_parallel_executions`
- Implement streaming for large operations
- Clear execution state regularly

#### Frequent Failures
- Review retry configuration
- Check error logs for patterns
- Adjust timeout values

### Debug Mode

Enable detailed logging:

```json
{
    "logging": {
        "level": "DEBUG"
    },
    "orchestration": {
        "progress_reporting": {
            "show_detailed_progress": true
        }
    }
}
```

### Performance Monitoring

```python
# Get performance metrics
summary = orchestrator.state_manager.get_execution_summary()
print(f"Total duration: {summary['total_duration']}s")
print(f"Average step time: {summary['total_duration'] / summary['total_executions']}s")
```

## Best Practices

1. **Plan Validation**: Always validate execution plans before execution
2. **Error Handling**: Configure appropriate retry strategies for different operation types
3. **Resource Management**: Monitor system resources during parallel execution
4. **Progress Reporting**: Use progress events for user feedback
5. **Testing**: Test complex execution plans with mock tools first
6. **Documentation**: Document custom execution patterns and configurations

## API Reference

### Core Classes

- `ToolOrchestrator`: Main orchestration engine
- `ExecutionPlan`: Represents a sequence of tool executions
- `ExecutionStep`: Individual tool execution step
- `RequestAnalyzer`: Analyzes user requests for multi-step needs
- `ErrorHandler`: Manages error handling and retry logic
- `ProgressReporter`: Provides real-time progress reporting

### Key Methods

- `orchestrator.analyze_and_execute(user_input)`: Analyze and execute user request
- `orchestrator.execute_plan(plan)`: Execute a custom execution plan
- `orchestrator.register_tool_executor(name, function)`: Register tool executor
- `plan.add_step(step)`: Add step to execution plan
- `plan.validate_plan()`: Validate execution plan
