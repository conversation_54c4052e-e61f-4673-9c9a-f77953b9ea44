# PyIDE - AI-Powered Python IDE

A Python implementation that replicates the AI interaction and file operations workflow from Cline, featuring secure file operations, real-time AI streaming, and comprehensive tool support.

## Features

- **AI Request-Response System**: OpenRouter API integration with streaming responses
- **Response Parsing Engine**: XML-based tool call extraction with state machine parser
- **Multi-Step Tool Execution**: Intelligent orchestration of complex operations with automatic planning
- **Temporary Workspace**: Dedicated temp directory for all AI operations with session tracking
- **File Operations**: Secure file operations with ignore patterns and user approval
- **Multi-file Project Support**: Create and manage complex projects with multiple related files
- **Parallel Execution**: Run independent operations simultaneously for better performance
- **Error Handling & Retry Logic**: Robust error handling with exponential backoff and smart retries
- **Progress Reporting**: Real-time progress updates for long-running multi-step operations
- **Diff-Based Editing**: Side-by-side diff view with atomic operations
- **Tool Support**: Complete set of development tools (read, write, execute, search)
- **Session Management**: Track file creation, modification, and relationships during AI sessions

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure API settings in `config.json`

3. Run the IDE:
```bash
python main.py
```

## Project Structure

```
pyIDE/
├── main.py                 # Main application entry point
├── config.json            # Configuration file
├── requirements.txt       # Python dependencies
├── core/
│   ├── __init__.py
│   ├── ai_client.py       # OpenRouter API client
│   ├── parser.py          # XML response parser
│   ├── tools/             # Tool implementations
│   │   ├── __init__.py
│   │   ├── file_ops.py    # File operation tools
│   │   ├── command.py     # Command execution
│   │   └── search.py      # File search tools
│   ├── security/          # Security and validation
│   │   ├── __init__.py
│   │   ├── ignore.py      # Ignore pattern handling
│   │   └── validator.py   # Path validation
│   └── ui/                # User interface
│       ├── __init__.py
│       ├── cli.py         # Command line interface
│       └── diff_view.py   # Diff viewer
├── examples/              # Example usage
│   └── demo.py
└── tests/                 # Test suite
    ├── __init__.py
    ├── test_parser.py
    ├── test_tools.py
    └── test_security.py
```

## Configuration

Edit `config.json` to configure API settings and workspace behavior:

```json
{
    "api": {
        "provider": "openrouter",
        "key": "your-api-key",
        "model": "deepseek/deepseek-r1-0528:free",
        "base_url": "https://openrouter.ai/api/v1"
    },
    "workspace": {
        "default_directory": ".",
        "temp_workspace": {
            "enabled": true,
            "directory_name": "temp",
            "use_session_subdirectories": false,
            "auto_create": true,
            "preserve_on_exit": true
        }
    },
    "security": {
        "max_file_size": 20971520,
        "workspace_only": true,
        "require_approval": true
    },
    "ui": {
        "interface": "cli",
        "show_diffs": true,
        "auto_approve": false
    }
}
```

### Temporary Workspace Configuration

The temporary workspace feature provides a dedicated directory for all AI operations:

- **`enabled`**: Enable/disable temporary workspace (default: true)
- **`directory_name`**: Name of the temp directory (default: "temp")
- **`use_session_subdirectories`**: Create session-specific subdirectories (default: false)
- **`auto_create`**: Automatically create temp directory on startup (default: true)
- **`preserve_on_exit`**: Keep temp files when PyIDE exits (default: true)

### Multi-Step Execution Configuration

The orchestration system provides intelligent multi-step tool execution:

- **`enabled`**: Enable/disable multi-step orchestration (default: true)
- **`max_parallel_executions`**: Maximum parallel tool executions (default: 5)
- **`enable_optimization`**: Enable execution plan optimization (default: true)
- **`continue_on_failure`**: Continue execution if some steps fail (default: false)

## Usage Examples

### Basic AI Interaction
```python
from core.ai_client import AIClient
from core.parser import ResponseParser

client = AIClient()
parser = ResponseParser()

response = await client.stream_request("Create a hello world Python script")
for chunk in response:
    content_blocks = parser.parse_streaming(chunk)
    # Process content blocks...
```

### File Operations
```python
from core.tools.file_ops import FileOperations

file_ops = FileOperations()
content = await file_ops.read_file("example.py")
await file_ops.write_file("new_file.py", "print('Hello, World!')")

# Create directories
await file_ops.create_directory("src")

# Get workspace information
workspace_info = await file_ops.get_workspace_info()

# Analyze file relationships
relationships = await file_ops.get_file_relationships("main.py")

# Get session information
session_info = await file_ops.get_session_info()
```

### Temporary Workspace Features

The temporary workspace provides several benefits:

1. **Isolated Environment**: All AI operations happen in a dedicated temp directory
2. **Session Tracking**: Track all files created, modified, or deleted during a session
3. **File Relationships**: Analyze imports and dependencies between files
4. **Multi-file Projects**: Support for complex projects with multiple related files
5. **Automatic Cleanup**: Optional cleanup of temporary files on exit

### Multi-Step Execution Features

The orchestration system automatically handles complex operations:

1. **Intelligent Analysis**: Detects when requests require multiple tool calls
2. **Automatic Planning**: Creates optimized execution plans with dependencies
3. **Parallel Execution**: Runs independent operations simultaneously
4. **Error Recovery**: Automatic retry with exponential backoff
5. **Progress Tracking**: Real-time updates for long-running operations
6. **Batch Optimization**: Groups similar operations for efficiency

#### Example Multi-Step Operations

**Project Analysis**:
```
User: "Check all my project files"
→ AI automatically:
  1. Lists all files in workspace
  2. Reads each file in parallel
  3. Analyzes code structure and dependencies
  4. Provides comprehensive project overview
```

**Batch File Operations**:
```
User: "Create a Python project structure"
→ AI automatically:
  1. Creates directory structure (src/, tests/, docs/)
  2. Generates project files (main.py, __init__.py, setup.py)
  3. Creates configuration files (requirements.txt, .gitignore)
  4. Sets up project documentation
```

## Security Features

- **Ignore Patterns**: `.pyideignore` file support (gitignore syntax)
- **Workspace Boundaries**: Restrict operations to workspace directory
- **File Size Limits**: Configurable maximum file size (default 20MB)
- **User Approval**: Interactive approval for all file operations
- **Path Validation**: Prevent directory traversal attacks

## Tool Reference

### File Operations
- `read_file`: Read file contents with encoding detection
- `write_to_file`: Create or overwrite files with user approval
- `replace_in_file`: Make targeted edits using SEARCH/REPLACE blocks
- `list_files`: List directory contents with filtering
- `search_files`: Search files using regex patterns

### System Operations
- `execute_command`: Run terminal commands with output capture
- `ask_followup_question`: Interactive user queries

### Development Tools
- Real-time diff viewing
- Syntax highlighting
- Error detection and reporting
- Auto-formatting detection

## API Integration

The system uses OpenRouter API with the following features:
- Streaming response handling
- Automatic retry logic
- Error handling and recovery
- Token usage tracking
- Cost monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details
