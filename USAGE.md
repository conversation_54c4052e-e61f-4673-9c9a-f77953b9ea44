# PyIDE Usage Guide

This guide explains how to use PyIDE, the AI-powered Python IDE with secure file operations and real-time interaction.

## Quick Start

### 1. Installation

```bash
# Navigate to the pyIDE directory
cd pyIDE

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

The system is pre-configured with the OpenRouter API key. You can modify `config.json` to adjust settings:

```json
{
    "api": {
        "provider": "openrouter",
        "key": "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",
        "model": "deepseek/deepseek-r1-0528:free"
    }
}
```

### 3. Running PyIDE

```bash
# Start PyIDE
python run.py

# Or run directly
python main.py
```

## Features Overview

### 🤖 AI Interaction
- **Real-time streaming**: AI responses stream in real-time
- **Tool integration**: AI can execute file operations and commands
- **Conversation history**: Maintains context across interactions
- **Error handling**: Robust error handling with retries

### 📁 File Operations
- **read_file**: Read file contents with encoding detection
- **write_to_file**: Create or overwrite files with user approval
- **replace_in_file**: Make targeted edits using SEARCH/REPLACE blocks
- **list_files**: List directory contents with filtering
- **search_files**: Search files using regex patterns

### 🔒 Security Features
- **Ignore patterns**: `.pyideignore` file controls AI access
- **User approval**: Interactive approval for all file operations
- **Workspace boundaries**: Restricts operations to workspace directory
- **Path validation**: Prevents directory traversal attacks
- **Command filtering**: Validates and filters dangerous commands

### 🎨 User Interface
- **Rich CLI**: Colorful command-line interface with syntax highlighting
- **Diff viewer**: Side-by-side diff view for file changes
- **Progress indicators**: Real-time progress and status updates
- **Interactive prompts**: User-friendly approval dialogs

## Usage Examples

### Basic AI Interaction

```
PyIDE> Create a Python script that prints hello world
