{"api": {"provider": "openrouter", "key": "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", "model": "deepseek/deepseek-chat-v3-0324:free", "base_url": "https://openrouter.ai/api/v1", "timeout": 60, "max_retries": 3, "retry_delay": 1.0}, "security": {"max_file_size": 20971520, "workspace_only": true, "require_approval": true, "ignore_file": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowed_extensions": [".py", ".txt", ".md", ".json", ".yaml", ".yml", ".toml", ".cfg", ".ini"], "blocked_paths": ["__pycache__", ".git", ".venv", "venv", "env", "node_modules"]}, "ui": {"interface": "cli", "show_diffs": true, "auto_approve": false, "colors": true, "diff_context_lines": 3, "max_display_lines": 50}, "tools": {"file_operations": {"read_file": true, "write_to_file": true, "replace_in_file": true, "list_files": true, "search_files": true, "create_directory": true}, "system_operations": {"execute_command": true, "ask_followup_question": true}, "advanced_features": {"browser_automation": false, "git_integration": true, "syntax_highlighting": true}}, "orchestration": {"enabled": true, "max_parallel_executions": 5, "default_timeout": 60.0, "max_parallel_steps": 5, "enable_optimization": true, "enable_batching": true, "continue_on_failure": false, "enable_dynamic_planning": true, "batch_timeout": 120.0, "multi_step_detection": {"enabled": true, "confidence_threshold": 0.5, "max_auto_steps": 20, "enable_implicit_detection": true}, "error_recovery": {"enabled": true, "auto_create_missing_files": true, "skip_permission_errors": true, "max_recovery_attempts": 3}, "progress_reporting": {"enabled": true, "show_detailed_progress": true, "show_estimated_time": true, "show_step_duration": true, "show_success_rate": true}, "retry_config": {"max_attempts": 3, "base_delay": 1.0, "max_delay": 60.0, "backoff_multiplier": 2.0, "jitter": true}, "batching": {"enabled": true, "max_batch_size": 10, "batch_similar_operations": true, "parallel_batch_execution": true}}, "logging": {"level": "INFO", "file": "pyide.log", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "max_size": 10485760, "backup_count": 5}, "workspace": {"default_directory": ".", "auto_detect_project": true, "project_files": ["pyproject.toml", "setup.py", "requirements.txt", "Pipfile"], "exclude_patterns": ["*.pyc", "*.pyo", "*.pyd", "__pycache__", ".pytest_cache"], "temp_workspace": {"enabled": true, "directory_name": "temp", "use_session_subdirectories": false, "auto_create": true, "preserve_on_exit": true}}}