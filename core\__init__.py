"""
PyIDE Core Module

This module contains the core functionality for the AI-powered IDE including:
- AI client for OpenRouter API integration
- Response parsing engine
- Tool implementations
- Security and validation
- User interface components
"""

__version__ = "1.0.0"
__author__ = "PyIDE Team"

from .ai_client import AIClient
from .parser import ResponseParser, ContentBlock, ToolUse, TextContent

__all__ = [
    "AIClient",
    "ResponseParser", 
    "ContentBlock",
    "ToolUse",
    "TextContent"
]
