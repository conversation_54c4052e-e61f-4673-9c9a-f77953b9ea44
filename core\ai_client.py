"""
AI Client for OpenRouter API Integration

This module implements the AI client that handles:
- System prompt construction with tool definitions
- Message formatting and conversation history
- Streaming API requests with error handling
- Real-time response processing
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, AsyncGenerator, Any
from dataclasses import dataclass
import aiohttp
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class StreamChunk:
    """Represents a chunk of streaming response data"""
    type: str  # "text", "usage", "error"
    content: str = ""
    usage: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@dataclass
class Message:
    """Represents a conversation message"""
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: Optional[float] = None


class AIClient:
    """
    AI Client for OpenRouter API integration with streaming support
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_config = config.get("api", {})
        self.base_url = self.api_config.get("base_url", "https://openrouter.ai/api/v1")
        self.api_key = self.api_config.get("key")
        self.model = self.api_config.get("model", "deepseek/deepseek-r1-0528:free")
        self.timeout = self.api_config.get("timeout", 60)
        self.max_retries = self.api_config.get("max_retries", 3)
        self.retry_delay = self.api_config.get("retry_delay", 1.0)
        
        self.conversation_history: List[Message] = []
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            
    def _get_headers(self) -> Dict[str, str]:
        """Get HTTP headers for API requests"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://pyide.local",
            "X-Title": "PyIDE"
        }
        
    def _build_system_prompt(self, workspace_path: str) -> str:
        """
        Build comprehensive system prompt with tool definitions
        Replicates the exact format from Cline's system prompt
        """
        return f"""You are PyIDE Assistant, a highly skilled software engineer with extensive knowledge in many programming languages, frameworks, design patterns, and best practices.

====

TOOL USE

You have access to a set of tools that are executed upon the user's approval. You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.

# Tool Use Formatting

Tool use is formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>

For example:

<read_file>
<path>src/main.py</path>
</read_file>

Always adhere to this format for the tool use to ensure proper parsing and execution.

# Available Tools

## read_file
Description: Read the contents of a file at the specified path. Use this when you need to examine the contents of an existing file.
Parameters:
- path: (required) The path of the file to read (relative to the current working directory {workspace_path})
Usage:
<read_file>
<path>File path here</path>
</read_file>

## write_to_file
Description: Request to write content to a file at the specified path. If the file exists, it will be overwritten with the provided content. If the file doesn't exist, it will be created.
Parameters:
- path: (required) The path of the file to write to (relative to the current working directory {workspace_path})
- content: (required) The content to write to the file. ALWAYS provide the COMPLETE intended content of the file.
Usage:
<write_to_file>
<path>File path here</path>
<content>
Your file content here
</content>
</write_to_file>

## replace_in_file
Description: Request to replace sections of content in an existing file using SEARCH/REPLACE blocks.
Parameters:
- path: (required) The path of the file to modify (relative to the current working directory {workspace_path})
- diff: (required) SEARCH/REPLACE blocks in this format:
  ```
  ------- SEARCH
  [exact content to find]
  =======
  [new content to replace with]
  +++++++ REPLACE
  ```
Usage:
<replace_in_file>
<path>File path here</path>
<diff>
Search and replace blocks here
</diff>
</replace_in_file>

## list_files
Description: List files and directories in a given path.
Parameters:
- path: (required) The path of the directory to list (relative to the current working directory {workspace_path})
- recursive: (optional) Whether to list files recursively (true/false, default: false)
Usage:
<list_files>
<path>Directory path here</path>
<recursive>false</recursive>
</list_files>

## search_files
Description: Search for files containing specific text patterns using regex.
Parameters:
- path: (required) The directory path to search in (relative to the current working directory {workspace_path})
- regex: (required) The regex pattern to search for
- file_pattern: (optional) File pattern to limit search (e.g., "*.py")
Usage:
<search_files>
<path>Directory path here</path>
<regex>Search pattern here</regex>
<file_pattern>*.py</file_pattern>
</search_files>

## execute_command
Description: Execute a command in the terminal and return the output.
Parameters:
- command: (required) The command to execute
Usage:
<execute_command>
<command>Command to execute</command>
</execute_command>

## ask_followup_question
Description: Ask the user a follow-up question to clarify requirements or get additional information.
Parameters:
- question: (required) The question to ask the user
Usage:
<ask_followup_question>
<question>Your question here</question>
</ask_followup_question>

# Tool Use Guidelines

1. Choose the most appropriate tool based on the task and the tool descriptions provided.
2. If multiple actions are needed, use one tool at a time per message to accomplish the task iteratively.
3. Formulate your tool use using the XML format specified for each tool.
4. After each tool use, wait for the result before proceeding.
5. ALWAYS wait for user confirmation after each tool use before proceeding.

====

IMPORTANT NOTES

- You are operating in the workspace directory: {workspace_path}
- All file paths should be relative to this workspace directory
- Always provide complete file content when using write_to_file
- Use replace_in_file for targeted edits to existing files
- Be careful with file operations and always confirm with the user
- Respect the user's project structure and coding conventions

You should be helpful, harmless, and honest. Always explain what you're doing and why."""
        
    def _format_messages(self, user_input: str, system_prompt: str) -> List[Dict[str, str]]:
        """Format messages for API request"""
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add conversation history
        for msg in self.conversation_history:
            messages.append({
                "role": msg.role,
                "content": msg.content
            })
            
        # Add current user input
        messages.append({
            "role": "user", 
            "content": user_input
        })
        
        return messages

    async def stream_request(self, user_input: str, workspace_path: str = ".") -> AsyncGenerator[StreamChunk, None]:
        """
        Stream AI response with real-time processing
        Implements retry logic and error handling
        """
        if not self.session:
            raise RuntimeError("AIClient must be used as async context manager")

        system_prompt = self._build_system_prompt(workspace_path)
        messages = self._format_messages(user_input, system_prompt)

        payload = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            "temperature": 0.0,
            "max_tokens": 4096
        }

        for attempt in range(self.max_retries + 1):
            try:
                async with self.session.post(
                    f"{self.base_url}/chat/completions",
                    headers=self._get_headers(),
                    json=payload
                ) as response:

                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"API request failed: {response.status} - {error_text}")

                        if attempt < self.max_retries:
                            await asyncio.sleep(self.retry_delay * (2 ** attempt))
                            continue
                        else:
                            yield StreamChunk(
                                type="error",
                                error=f"API request failed: {response.status} - {error_text}"
                            )
                            return

                    assistant_content = ""
                    async for line in response.content:
                        line = line.decode('utf-8').strip()

                        if not line or not line.startswith('data: '):
                            continue

                        if line == 'data: [DONE]':
                            break

                        try:
                            data = json.loads(line[6:])  # Remove 'data: ' prefix

                            if 'choices' in data and data['choices']:
                                choice = data['choices'][0]

                                if 'delta' in choice and 'content' in choice['delta']:
                                    content = choice['delta']['content']
                                    if content:
                                        assistant_content += content
                                        yield StreamChunk(type="text", content=content)

                                if choice.get('finish_reason') == 'stop':
                                    # Add to conversation history
                                    self.conversation_history.append(
                                        Message(role="user", content=user_input)
                                    )
                                    self.conversation_history.append(
                                        Message(role="assistant", content=assistant_content)
                                    )
                                    break

                            if 'usage' in data:
                                yield StreamChunk(type="usage", usage=data['usage'])

                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse streaming response: {e}")
                            continue

                return  # Success, exit retry loop

            except asyncio.TimeoutError:
                logger.warning(f"Request timeout on attempt {attempt + 1}")
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    yield StreamChunk(type="error", error="Request timeout")
                    return

            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    yield StreamChunk(type="error", error=f"Unexpected error: {e}")
                    return

    async def simple_request(self, user_input: str, workspace_path: str = ".") -> str:
        """
        Make a simple non-streaming request
        """
        full_response = ""
        async for chunk in self.stream_request(user_input, workspace_path):
            if chunk.type == "text":
                full_response += chunk.content
            elif chunk.type == "error":
                raise RuntimeError(f"API Error: {chunk.error}")

        return full_response

    def add_message(self, role: str, content: str):
        """Add a message to conversation history"""
        self.conversation_history.append(Message(role=role, content=content))

    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history.clear()

    def get_history(self) -> List[Message]:
        """Get conversation history"""
        return self.conversation_history.copy()

    def get_token_count(self) -> int:
        """Estimate token count for current conversation"""
        total_chars = sum(len(msg.content) for msg in self.conversation_history)
        return total_chars // 4  # Rough estimate: 4 chars per token
