"""
Dynamic Execution Plan Generation

This module provides dynamic planning capabilities that can adapt execution plans
based on intermediate results and changing conditions:
- Adaptive plan modification
- Result-based step generation
- Dynamic dependency resolution
- Context-aware planning
"""

import logging
import uuid
from typing import Dict, List, Optional, Any, Set, Callable
from dataclasses import dataclass
from enum import Enum

from .execution_plan import ExecutionPlan, ExecutionStep, ExecutionMode
from .execution_state import ExecutionState, ExecutionStatus

logger = logging.getLogger(__name__)


class PlanModificationType(Enum):
    """Types of plan modifications"""
    ADD_STEP = "add_step"
    REMOVE_STEP = "remove_step"
    MODIFY_STEP = "modify_step"
    REORDER_STEPS = "reorder_steps"
    ADD_DEPENDENCY = "add_dependency"
    REMOVE_DEPENDENCY = "remove_dependency"


@dataclass
class PlanModification:
    """Represents a modification to an execution plan"""
    modification_type: PlanModificationType
    step_id: Optional[str] = None
    new_step: Optional[ExecutionStep] = None
    target_step_id: Optional[str] = None
    dependency_id: Optional[str] = None
    reason: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class DynamicPlanner:
    """
    Dynamic execution planner that can adapt plans based on intermediate results
    """
    
    def __init__(self):
        self.result_handlers: Dict[str, Callable] = {}
        self.condition_evaluators: Dict[str, Callable] = {}
        self.plan_templates: Dict[str, ExecutionPlan] = {}
        
    def register_result_handler(self, tool_name: str, handler: Callable):
        """Register a handler for processing tool results"""
        self.result_handlers[tool_name] = handler
        logger.info(f"Registered result handler for tool: {tool_name}")
        
    def register_condition_evaluator(self, condition_name: str, evaluator: Callable):
        """Register a condition evaluator for dynamic planning"""
        self.condition_evaluators[condition_name] = evaluator
        logger.info(f"Registered condition evaluator: {condition_name}")
        
    def register_plan_template(self, template_name: str, plan: ExecutionPlan):
        """Register a plan template for reuse"""
        self.plan_templates[template_name] = plan
        logger.info(f"Registered plan template: {template_name}")
        
    def adapt_plan_based_on_result(self, plan: ExecutionPlan, step_id: str, result: Dict[str, Any]) -> List[PlanModification]:
        """Adapt execution plan based on intermediate result"""
        modifications = []
        
        # Get the completed step
        completed_step = plan.get_step(step_id)
        if not completed_step:
            return modifications
            
        # Check if we have a result handler for this tool
        if completed_step.tool_name in self.result_handlers:
            handler = self.result_handlers[completed_step.tool_name]
            try:
                handler_modifications = handler(plan, completed_step, result)
                if handler_modifications:
                    modifications.extend(handler_modifications)
            except Exception as e:
                logger.error(f"Error in result handler for {completed_step.tool_name}: {e}")
                
        # Apply built-in adaptation logic
        built_in_modifications = self._apply_built_in_adaptations(plan, completed_step, result)
        modifications.extend(built_in_modifications)
        
        return modifications
        
    def _apply_built_in_adaptations(self, plan: ExecutionPlan, completed_step: ExecutionStep, result: Dict[str, Any]) -> List[PlanModification]:
        """Apply built-in adaptation logic based on common patterns"""
        modifications = []
        
        # Handle list_files results - add read_file steps for discovered files
        if completed_step.tool_name == "list_files" and result.get("success"):
            files = result.get("files", [])
            if files:
                modifications.extend(self._create_read_file_steps(plan, files, completed_step.step_id))
                
        # Handle search_files results - add read_file steps for matching files
        elif completed_step.tool_name == "search_files" and result.get("success"):
            matches = result.get("matches", [])
            if matches:
                file_paths = [match.get("file", "") for match in matches if match.get("file")]
                if file_paths:
                    modifications.extend(self._create_read_file_steps(plan, file_paths, completed_step.step_id))
                    
        # Handle read_file results - check for conditional operations
        elif completed_step.tool_name == "read_file" and result.get("success"):
            content = result.get("content", "")
            modifications.extend(self._check_conditional_operations(plan, completed_step, content))
            
        return modifications
        
    def _create_read_file_steps(self, plan: ExecutionPlan, file_paths: List[str], dependency_step_id: str) -> List[PlanModification]:
        """Create read_file steps for a list of file paths"""
        modifications = []
        
        for file_path in file_paths:
            # Skip if we already have a read step for this file
            if self._has_read_step_for_file(plan, file_path):
                continue
                
            step_id = f"read_file_{uuid.uuid4().hex[:8]}"
            new_step = ExecutionStep(
                step_id=step_id,
                tool_name="read_file",
                params={"path": file_path},
                dependencies={dependency_step_id},
                mode=ExecutionMode.PARALLEL,
                tags={"auto_generated", "read_discovered_file"}
            )
            
            modifications.append(PlanModification(
                modification_type=PlanModificationType.ADD_STEP,
                new_step=new_step,
                reason=f"Auto-generated read step for discovered file: {file_path}"
            ))
            
        return modifications
        
    def _has_read_step_for_file(self, plan: ExecutionPlan, file_path: str) -> bool:
        """Check if plan already has a read step for the given file"""
        for step in plan:
            if step.tool_name == "read_file" and step.params.get("path") == file_path:
                return True
        return False
        
    def _check_conditional_operations(self, plan: ExecutionPlan, completed_step: ExecutionStep, content: str) -> List[PlanModification]:
        """Check if file content triggers conditional operations"""
        modifications = []
        
        # Example: If file contains TODO comments, suggest creating a task list
        if "TODO" in content.upper() or "FIXME" in content.upper():
            step_id = f"create_todo_list_{uuid.uuid4().hex[:8]}"
            new_step = ExecutionStep(
                step_id=step_id,
                tool_name="write_to_file",
                params={
                    "path": "TODO.md",
                    "content": f"# TODO List extracted from {completed_step.params.get('path', 'file')}\n\n"
                },
                dependencies={completed_step.step_id},
                mode=ExecutionMode.SEQUENTIAL,
                tags={"auto_generated", "todo_extraction"}
            )
            
            modifications.append(PlanModification(
                modification_type=PlanModificationType.ADD_STEP,
                new_step=new_step,
                reason="Auto-generated TODO list creation based on file content"
            ))
            
        # Example: If file is a Python script, suggest running it
        file_path = completed_step.params.get("path", "")
        if file_path.endswith(".py") and "if __name__ == '__main__':" in content:
            step_id = f"run_script_{uuid.uuid4().hex[:8]}"
            new_step = ExecutionStep(
                step_id=step_id,
                tool_name="execute_command",
                params={"command": f"python {file_path}"},
                dependencies={completed_step.step_id},
                mode=ExecutionMode.SEQUENTIAL,
                tags={"auto_generated", "script_execution"}
            )
            
            modifications.append(PlanModification(
                modification_type=PlanModificationType.ADD_STEP,
                new_step=new_step,
                reason="Auto-generated script execution based on main block detection"
            ))
            
        return modifications
        
    def apply_modifications(self, plan: ExecutionPlan, modifications: List[PlanModification]) -> bool:
        """Apply a list of modifications to an execution plan"""
        try:
            for modification in modifications:
                self._apply_single_modification(plan, modification)
                logger.info(f"Applied modification: {modification.modification_type.value} - {modification.reason}")
            return True
        except Exception as e:
            logger.error(f"Error applying plan modifications: {e}")
            return False
            
    def _apply_single_modification(self, plan: ExecutionPlan, modification: PlanModification):
        """Apply a single modification to the plan"""
        if modification.modification_type == PlanModificationType.ADD_STEP:
            if modification.new_step:
                plan.add_step(modification.new_step)
                
        elif modification.modification_type == PlanModificationType.REMOVE_STEP:
            if modification.step_id:
                plan.remove_step(modification.step_id)
                
        elif modification.modification_type == PlanModificationType.MODIFY_STEP:
            if modification.step_id and modification.new_step:
                plan.update_step(modification.step_id, modification.new_step)
                
        elif modification.modification_type == PlanModificationType.ADD_DEPENDENCY:
            if modification.step_id and modification.dependency_id:
                plan.add_dependency(modification.step_id, modification.dependency_id)
                
        elif modification.modification_type == PlanModificationType.REMOVE_DEPENDENCY:
            if modification.step_id and modification.dependency_id:
                plan.remove_dependency(modification.step_id, modification.dependency_id)
                
    def evaluate_conditions(self, plan: ExecutionPlan, context: Dict[str, Any]) -> List[PlanModification]:
        """Evaluate registered conditions and return modifications"""
        modifications = []
        
        for condition_name, evaluator in self.condition_evaluators.items():
            try:
                condition_modifications = evaluator(plan, context)
                if condition_modifications:
                    modifications.extend(condition_modifications)
            except Exception as e:
                logger.error(f"Error evaluating condition {condition_name}: {e}")
                
        return modifications
        
    def create_adaptive_plan_from_template(self, template_name: str, context: Dict[str, Any]) -> Optional[ExecutionPlan]:
        """Create an adaptive plan from a registered template"""
        if template_name not in self.plan_templates:
            logger.error(f"Plan template not found: {template_name}")
            return None
            
        template = self.plan_templates[template_name]
        
        # Create a copy of the template
        new_plan = ExecutionPlan(name=f"Adaptive_{template.name}")
        
        # Copy steps from template
        for step in template:
            new_step = ExecutionStep(
                step_id=step.step_id,
                tool_name=step.tool_name,
                params=step.params.copy(),
                dependencies=step.dependencies.copy(),
                mode=step.mode,
                condition=step.condition,
                retry_count=step.retry_count,
                timeout=step.timeout,
                tags=step.tags.copy(),
                metadata=step.metadata.copy()
            )
            new_plan.add_step(new_step)
            
        # Apply context-based modifications
        context_modifications = self.evaluate_conditions(new_plan, context)
        self.apply_modifications(new_plan, context_modifications)
        
        return new_plan
