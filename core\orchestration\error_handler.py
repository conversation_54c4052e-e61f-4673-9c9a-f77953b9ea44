"""
Error Handling and Retry Logic

This module provides comprehensive error handling for tool executions:
- Exponential backoff retry strategies
- Error classification and handling
- Recovery mechanisms
- Failure analysis and reporting
"""

import asyncio
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Awaitable
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """Classification of error types"""
    NETWORK_ERROR = "network_error"
    FILE_ERROR = "file_error"
    PERMISSION_ERROR = "permission_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    DEPENDENCY_ERROR = "dependency_error"
    RESOURCE_ERROR = "resource_error"
    UNKNOWN_ERROR = "unknown_error"


class RetryStrategy(Enum):
    """Retry strategy types"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_DELAY = "fixed_delay"
    IMMEDIATE = "immediate"
    NO_RETRY = "no_retry"


@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retry_on_errors: List[ErrorType] = None
    
    def __post_init__(self):
        if self.retry_on_errors is None:
            self.retry_on_errors = [
                ErrorType.NETWORK_ERROR,
                ErrorType.TIMEOUT_ERROR,
                ErrorType.RESOURCE_ERROR,
                ErrorType.UNKNOWN_ERROR  # Allow retrying unknown errors for testing
            ]


@dataclass
class ErrorInfo:
    """Information about an error"""
    error_type: ErrorType
    message: str
    timestamp: datetime
    execution_id: str
    tool_name: str
    attempt_number: int
    is_retryable: bool
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "error_type": self.error_type.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "execution_id": self.execution_id,
            "tool_name": self.tool_name,
            "attempt_number": self.attempt_number,
            "is_retryable": self.is_retryable,
            "context": self.context
        }


class ErrorHandler:
    """
    Handles errors and implements retry logic for tool executions
    """
    
    def __init__(self, default_config: Optional[RetryConfig] = None):
        self.default_config = default_config or RetryConfig()
        self.error_history: List[ErrorInfo] = []
        self.tool_configs: Dict[str, RetryConfig] = {}
        self.error_patterns: Dict[str, ErrorType] = {
            "permission denied": ErrorType.PERMISSION_ERROR,
            "file not found": ErrorType.FILE_ERROR,
            "no such file": ErrorType.FILE_ERROR,
            "connection": ErrorType.NETWORK_ERROR,
            "timeout": ErrorType.TIMEOUT_ERROR,
            "timed out": ErrorType.TIMEOUT_ERROR,
            "network": ErrorType.NETWORK_ERROR,
            "validation": ErrorType.VALIDATION_ERROR,
            "invalid": ErrorType.VALIDATION_ERROR,
            "dependency": ErrorType.DEPENDENCY_ERROR,
            "resource": ErrorType.RESOURCE_ERROR,
            "memory": ErrorType.RESOURCE_ERROR,
            "disk": ErrorType.RESOURCE_ERROR
        }
        
    def set_tool_config(self, tool_name: str, config: RetryConfig):
        """Set retry configuration for a specific tool"""
        self.tool_configs[tool_name] = config
        logger.info(f"Set retry config for tool {tool_name}: {config.max_attempts} attempts")
        
    def classify_error(self, error_message: str, exception: Optional[Exception] = None) -> ErrorType:
        """Classify an error based on message and exception type"""
        error_lower = error_message.lower()
        
        # Check exception type first
        if exception:
            if isinstance(exception, PermissionError):
                return ErrorType.PERMISSION_ERROR
            elif isinstance(exception, FileNotFoundError):
                return ErrorType.FILE_ERROR
            elif isinstance(exception, TimeoutError):
                return ErrorType.TIMEOUT_ERROR
            elif isinstance(exception, ConnectionError):
                return ErrorType.NETWORK_ERROR
            elif isinstance(exception, ValueError):
                return ErrorType.VALIDATION_ERROR
                
        # Check error message patterns
        for pattern, error_type in self.error_patterns.items():
            if pattern in error_lower:
                return error_type

        return ErrorType.UNKNOWN_ERROR
        
    def is_retryable(self, error_type: ErrorType, tool_name: str) -> bool:
        """Check if an error is retryable for a specific tool"""
        config = self.tool_configs.get(tool_name, self.default_config)
        return error_type in config.retry_on_errors
        
    def calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """Calculate delay before next retry attempt"""
        if config.strategy == RetryStrategy.NO_RETRY:
            return 0.0
        elif config.strategy == RetryStrategy.IMMEDIATE:
            return 0.0
        elif config.strategy == RetryStrategy.FIXED_DELAY:
            delay = config.base_delay
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * attempt
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_multiplier ** (attempt - 1))
        else:
            delay = config.base_delay
            
        # Apply maximum delay limit
        delay = min(delay, config.max_delay)
        
        # Add jitter to prevent thundering herd
        if config.jitter:
            jitter_amount = delay * 0.1 * random.random()
            delay += jitter_amount
            
        return delay
        
    async def execute_with_retry(
        self,
        execution_id: str,
        tool_name: str,
        operation: Callable[[], Awaitable[Any]],
        config: Optional[RetryConfig] = None
    ) -> Any:
        """Execute an operation with retry logic"""
        retry_config = config or self.tool_configs.get(tool_name, self.default_config)
        last_error = None
        
        for attempt in range(1, retry_config.max_attempts + 1):
            try:
                logger.debug(f"Executing {tool_name} (attempt {attempt}/{retry_config.max_attempts})")
                result = await operation()
                
                # Success - clear any previous errors for this execution
                self._clear_execution_errors(execution_id)
                return result
                
            except Exception as e:
                error_type = self.classify_error(str(e), e)
                is_retryable = self.is_retryable(error_type, tool_name)
                
                error_info = ErrorInfo(
                    error_type=error_type,
                    message=str(e),
                    timestamp=datetime.now(),
                    execution_id=execution_id,
                    tool_name=tool_name,
                    attempt_number=attempt,
                    is_retryable=is_retryable,
                    context={"exception_type": type(e).__name__}
                )
                
                self.error_history.append(error_info)
                last_error = error_info
                
                logger.warning(f"Execution failed (attempt {attempt}): {tool_name} - {error_type.value} - {str(e)}")
                
                # Check if we should retry
                if not is_retryable or attempt >= retry_config.max_attempts:
                    logger.error(f"Execution failed permanently: {tool_name} - {str(e)}")
                    raise e
                    
                # Calculate delay and wait
                delay = self.calculate_delay(attempt, retry_config)
                if delay > 0:
                    logger.info(f"Retrying {tool_name} in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
                    
        # This should never be reached, but just in case
        if last_error:
            raise Exception(f"Max retries exceeded: {last_error.message}")
        else:
            raise Exception("Unknown error during retry execution")
            
    def _clear_execution_errors(self, execution_id: str):
        """Clear errors for a specific execution ID"""
        self.error_history = [e for e in self.error_history if e.execution_id != execution_id]
        
    def get_error_summary(self, execution_id: Optional[str] = None) -> Dict[str, Any]:
        """Get summary of errors"""
        errors = self.error_history
        if execution_id:
            errors = [e for e in errors if e.execution_id == execution_id]
            
        error_counts = {}
        for error_type in ErrorType:
            error_counts[error_type.value] = len([e for e in errors if e.error_type == error_type])
            
        retryable_errors = len([e for e in errors if e.is_retryable])
        permanent_errors = len([e for e in errors if not e.is_retryable])
        
        return {
            "total_errors": len(errors),
            "error_counts": error_counts,
            "retryable_errors": retryable_errors,
            "permanent_errors": permanent_errors,
            "recent_errors": [e.to_dict() for e in errors[-10:]]  # Last 10 errors
        }
        
    def get_tool_error_stats(self, tool_name: str) -> Dict[str, Any]:
        """Get error statistics for a specific tool"""
        tool_errors = [e for e in self.error_history if e.tool_name == tool_name]
        
        if not tool_errors:
            return {"tool_name": tool_name, "error_count": 0}
            
        error_types = {}
        for error in tool_errors:
            error_type = error.error_type.value
            if error_type not in error_types:
                error_types[error_type] = 0
            error_types[error_type] += 1
            
        avg_attempts = sum(e.attempt_number for e in tool_errors) / len(tool_errors)
        
        return {
            "tool_name": tool_name,
            "error_count": len(tool_errors),
            "error_types": error_types,
            "average_attempts": avg_attempts,
            "last_error": tool_errors[-1].to_dict() if tool_errors else None
        }
        
    def suggest_config_adjustments(self, tool_name: str) -> Dict[str, Any]:
        """Suggest retry configuration adjustments based on error history"""
        stats = self.get_tool_error_stats(tool_name)
        suggestions = []
        
        if stats["error_count"] == 0:
            return {"tool_name": tool_name, "suggestions": ["No errors recorded"]}
            
        # Analyze error patterns
        error_types = stats.get("error_types", {})
        
        if error_types.get("timeout_error", 0) > 0:
            suggestions.append("Consider increasing timeout values")
            
        if error_types.get("network_error", 0) > 0:
            suggestions.append("Consider increasing retry attempts for network operations")
            
        if error_types.get("resource_error", 0) > 0:
            suggestions.append("Consider adding delays between operations")
            
        if stats.get("average_attempts", 0) > 2:
            suggestions.append("Consider increasing base delay or using exponential backoff")
            
        return {
            "tool_name": tool_name,
            "suggestions": suggestions,
            "current_stats": stats
        }
        
    def clear_history(self, older_than: Optional[timedelta] = None):
        """Clear error history"""
        if older_than:
            cutoff_time = datetime.now() - older_than
            self.error_history = [e for e in self.error_history if e.timestamp > cutoff_time]
        else:
            self.error_history.clear()
            
        logger.info("Cleared error history")
        
    def export_errors(self) -> List[Dict[str, Any]]:
        """Export all errors as list of dictionaries"""
        return [error.to_dict() for error in self.error_history]
