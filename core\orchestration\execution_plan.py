"""
Execution Plan Management

This module manages execution plans for multi-step tool operations:
- Plan creation and validation
- Dependency management
- Execution sequencing
- Plan optimization
"""

import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
import uuid
from enum import Enum

from .execution_state import ExecutionState, ExecutionStatus

logger = logging.getLogger(__name__)


class ExecutionMode(Enum):
    """Execution mode for steps"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"


@dataclass
class ExecutionStep:
    """
    Represents a single step in an execution plan
    """
    step_id: str
    tool_name: str
    params: Dict[str, Any]
    dependencies: Set[str]
    mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    condition: Optional[str] = None
    retry_count: int = 3
    timeout: Optional[float] = None
    tags: Set[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = set()
        if self.metadata is None:
            self.metadata = {}

    def add_tag(self, tag: str):
        """Add a tag to this step"""
        self.tags.add(tag)

    def has_tag(self, tag: str) -> bool:
        """Check if step has a specific tag"""
        return tag in self.tags

    def add_dependency(self, dependency_id: str):
        """Add a dependency to this step"""
        self.dependencies.add(dependency_id)
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert step to dictionary"""
        return {
            "step_id": self.step_id,
            "tool_name": self.tool_name,
            "params": self.params,
            "dependencies": list(self.dependencies),
            "mode": self.mode.value,
            "condition": self.condition,
            "retry_count": self.retry_count,
            "timeout": self.timeout,
            "tags": list(self.tags),
            "metadata": self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExecutionStep':
        """Create step from dictionary"""
        return cls(
            step_id=data["step_id"],
            tool_name=data["tool_name"],
            params=data["params"],
            dependencies=set(data["dependencies"]),
            mode=ExecutionMode(data["mode"]),
            condition=data["condition"],
            retry_count=data["retry_count"],
            timeout=data["timeout"],
            tags=set(data["tags"]),
            metadata=data["metadata"]
        )


class ExecutionPlan:
    """
    Manages a complete execution plan with multiple steps
    """
    
    def __init__(self, plan_id: Optional[str] = None, name: Optional[str] = None):
        self.plan_id = plan_id or str(uuid.uuid4())
        self.name = name or f"Plan_{self.plan_id[:8]}"
        self.steps: Dict[str, ExecutionStep] = {}
        self.step_order: List[str] = []
        self.metadata: Dict[str, Any] = {}
        self.created_at = None
        self.estimated_duration: Optional[float] = None
        
    def add_step(self, step: ExecutionStep) -> str:
        """Add a step to the execution plan"""
        if step.step_id in self.steps:
            raise ValueError(f"Step with ID {step.step_id} already exists")
            
        self.steps[step.step_id] = step
        self.step_order.append(step.step_id)
        
        logger.info(f"Added step {step.step_id} ({step.tool_name}) to plan {self.plan_id}")
        return step.step_id
        
    def add_dependency(self, step_id: str, dependency_id: str):
        """Add a dependency between steps"""
        if step_id not in self.steps:
            raise ValueError(f"Step {step_id} not found")
        if dependency_id not in self.steps:
            raise ValueError(f"Dependency step {dependency_id} not found")
            
        self.steps[step_id].dependencies.add(dependency_id)
        logger.info(f"Added dependency: {step_id} depends on {dependency_id}")
        
    def remove_step(self, step_id: str):
        """Remove a step from the plan"""
        if step_id not in self.steps:
            raise ValueError(f"Step {step_id} not found")
            
        # Remove dependencies to this step from other steps
        for step in self.steps.values():
            step.dependencies.discard(step_id)
            
        del self.steps[step_id]
        self.step_order.remove(step_id)
        
        logger.info(f"Removed step {step_id} from plan {self.plan_id}")
        
    def get_step(self, step_id: str) -> Optional[ExecutionStep]:
        """Get a step by ID"""
        return self.steps.get(step_id)
        
    def get_steps_by_tool(self, tool_name: str) -> List[ExecutionStep]:
        """Get all steps that use a specific tool"""
        return [step for step in self.steps.values() if step.tool_name == tool_name]
        
    def get_ready_steps(self, completed_steps: Set[str]) -> List[ExecutionStep]:
        """Get steps that are ready to execute (all dependencies completed)"""
        ready_steps = []
        
        for step in self.steps.values():
            if step.dependencies.issubset(completed_steps):
                ready_steps.append(step)
                
        return ready_steps
        
    def get_parallel_groups(self) -> List[List[ExecutionStep]]:
        """Get groups of steps that can be executed in parallel"""
        groups = []
        completed_steps = set()
        
        while len(completed_steps) < len(self.steps):
            ready_steps = self.get_ready_steps(completed_steps)
            
            if not ready_steps:
                # Check for circular dependencies
                remaining_steps = set(self.steps.keys()) - completed_steps
                raise ValueError(f"Circular dependency detected in steps: {remaining_steps}")
                
            # Group parallel steps
            parallel_group = []
            sequential_group = []
            
            for step in ready_steps:
                if step.mode == ExecutionMode.PARALLEL:
                    parallel_group.append(step)
                else:
                    sequential_group.append(step)
                    
            # Add parallel group if exists
            if parallel_group:
                groups.append(parallel_group)
                
            # Add sequential steps one by one
            for step in sequential_group:
                groups.append([step])
                
            # Mark these steps as completed for next iteration
            completed_steps.update(step.step_id for step in ready_steps)
            
        return groups
        
    def validate_plan(self) -> Tuple[bool, List[str]]:
        """Validate the execution plan for consistency"""
        errors = []
        
        # Check for circular dependencies
        try:
            self.get_parallel_groups()
        except ValueError as e:
            errors.append(str(e))
            
        # Check that all dependencies exist
        for step in self.steps.values():
            for dep_id in step.dependencies:
                if dep_id not in self.steps:
                    errors.append(f"Step {step.step_id} depends on non-existent step {dep_id}")
                    
        # Check for self-dependencies
        for step in self.steps.values():
            if step.step_id in step.dependencies:
                errors.append(f"Step {step.step_id} has self-dependency")
                
        return len(errors) == 0, errors
        
    def optimize_plan(self):
        """Optimize the execution plan for efficiency"""
        # Group similar operations
        tool_groups = {}
        for step in self.steps.values():
            if step.tool_name not in tool_groups:
                tool_groups[step.tool_name] = []
            tool_groups[step.tool_name].append(step)

        # Optimize different types of operations
        self._optimize_read_operations(tool_groups)
        self._optimize_write_operations(tool_groups)
        self._optimize_search_operations(tool_groups)
        self._optimize_directory_operations(tool_groups)

        logger.info(f"Optimized plan {self.plan_id}")

    def _optimize_read_operations(self, tool_groups: Dict[str, List[ExecutionStep]]):
        """Optimize read operations for parallel execution"""
        read_steps = tool_groups.get("read_file", [])
        list_steps = tool_groups.get("list_files", [])

        # Read operations can often be parallelized if they don't depend on each other
        for step in read_steps + list_steps:
            if not step.dependencies:
                step.mode = ExecutionMode.PARALLEL
                step.add_tag("batch_read")

    def _optimize_write_operations(self, tool_groups: Dict[str, List[ExecutionStep]]):
        """Optimize write operations"""
        write_steps = tool_groups.get("write_to_file", [])

        # Group writes by directory to optimize directory creation
        dir_groups = {}
        for step in write_steps:
            file_path = step.params.get("path", "")
            dir_path = "/".join(file_path.split("/")[:-1]) if "/" in file_path else "."

            if dir_path not in dir_groups:
                dir_groups[dir_path] = []
            dir_groups[dir_path].append(step)

        # For each directory, ensure directory creation happens first
        for dir_path, steps in dir_groups.items():
            if len(steps) > 1:
                # Add batch tag for similar operations
                for step in steps:
                    step.add_tag("batch_write")
                    step.add_tag(f"dir_{dir_path}")

    def _optimize_search_operations(self, tool_groups: Dict[str, List[ExecutionStep]]):
        """Optimize search operations"""
        search_steps = tool_groups.get("search_files", [])

        # Search operations can be parallelized if they search different patterns
        pattern_groups = {}
        for step in search_steps:
            pattern = step.params.get("regex", "")
            if pattern not in pattern_groups:
                pattern_groups[pattern] = []
            pattern_groups[pattern].append(step)

        # Mark different patterns for parallel execution
        for pattern, steps in pattern_groups.items():
            if len(steps) == 1:
                steps[0].mode = ExecutionMode.PARALLEL
                steps[0].add_tag("batch_search")

    def _optimize_directory_operations(self, tool_groups: Dict[str, List[ExecutionStep]]):
        """Optimize directory creation operations"""
        dir_steps = tool_groups.get("create_directory", [])

        # Sort directory creation by depth (create parent directories first)
        dir_steps.sort(key=lambda s: s.params.get("path", "").count("/"))

        # Add dependencies between nested directories
        for i, step in enumerate(dir_steps):
            step_path = step.params.get("path", "")

            # Check if this directory is nested under any previous directory
            for j in range(i):
                parent_step = dir_steps[j]
                parent_path = parent_step.params.get("path", "")

                if step_path.startswith(parent_path + "/"):
                    step.add_dependency(parent_step.step_id)
                    break

        # Mark independent directory creations for parallel execution
        for step in dir_steps:
            if not step.dependencies:
                step.mode = ExecutionMode.PARALLEL
                step.add_tag("batch_directory")
        
    def estimate_duration(self, tool_durations: Dict[str, float]) -> float:
        """Estimate total execution duration"""
        groups = self.get_parallel_groups()
        total_duration = 0.0
        
        for group in groups:
            if len(group) == 1:
                # Sequential execution
                step = group[0]
                duration = tool_durations.get(step.tool_name, 1.0)
                total_duration += duration
            else:
                # Parallel execution - use maximum duration in group
                max_duration = max(tool_durations.get(step.tool_name, 1.0) for step in group)
                total_duration += max_duration
                
        self.estimated_duration = total_duration
        return total_duration
        
    def get_execution_order(self) -> List[str]:
        """Get the optimal execution order for steps"""
        groups = self.get_parallel_groups()
        execution_order = []
        
        for group in groups:
            # Sort by step order within group
            group_sorted = sorted(group, key=lambda s: self.step_order.index(s.step_id))
            execution_order.extend(step.step_id for step in group_sorted)
            
        return execution_order
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert plan to dictionary for serialization"""
        return {
            "plan_id": self.plan_id,
            "name": self.name,
            "steps": {step_id: step.to_dict() for step_id, step in self.steps.items()},
            "step_order": self.step_order.copy(),
            "metadata": self.metadata,
            "estimated_duration": self.estimated_duration
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExecutionPlan':
        """Create plan from dictionary"""
        plan = cls(plan_id=data["plan_id"], name=data["name"])
        plan.step_order = data["step_order"].copy()
        plan.metadata = data["metadata"]
        plan.estimated_duration = data["estimated_duration"]
        
        for step_id, step_data in data["steps"].items():
            step = ExecutionStep.from_dict(step_data)
            plan.steps[step_id] = step
            
        return plan
        
    def __len__(self) -> int:
        """Get number of steps in plan"""
        return len(self.steps)
        
    def __contains__(self, step_id: str) -> bool:
        """Check if step exists in plan"""
        return step_id in self.steps
        
    def __iter__(self):
        """Iterate over steps in order"""
        for step_id in self.step_order:
            yield self.steps[step_id]
