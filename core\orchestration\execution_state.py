"""
Execution State Management

This module manages the state of tool executions including:
- Execution status tracking (pending, executing, completed, failed)
- Result storage and retrieval
- Execution timing and performance metrics
- State persistence and recovery
"""

import json
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
import uuid

logger = logging.getLogger(__name__)


class ExecutionStatus(Enum):
    """Execution status enumeration"""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


@dataclass
class ExecutionMetrics:
    """Execution performance metrics"""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    retry_count: int = 0
    memory_usage: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": self.duration,
            "retry_count": self.retry_count,
            "memory_usage": self.memory_usage
        }


@dataclass
class ExecutionResult:
    """Result of a tool execution"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    error_type: Optional[str] = None
    output: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


class ExecutionState:
    """
    Manages the execution state of individual tool calls
    """
    
    def __init__(self, execution_id: str, tool_name: str, params: Dict[str, Any]):
        self.execution_id = execution_id
        self.tool_name = tool_name
        self.params = params
        self.status = ExecutionStatus.PENDING
        self.result: Optional[ExecutionResult] = None
        self.metrics = ExecutionMetrics()
        self.dependencies: Set[str] = set()
        self.dependents: Set[str] = set()
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.tags: Set[str] = set()
        self.metadata: Dict[str, Any] = {}
        
    def start_execution(self):
        """Mark execution as started"""
        self.status = ExecutionStatus.EXECUTING
        self.metrics.start_time = datetime.now()
        self.updated_at = datetime.now()
        logger.info(f"Started execution: {self.execution_id} ({self.tool_name})")
        
    def complete_execution(self, result: ExecutionResult):
        """Mark execution as completed with result"""
        self.status = ExecutionStatus.COMPLETED
        self.result = result
        self.metrics.end_time = datetime.now()
        if self.metrics.start_time:
            self.metrics.duration = (self.metrics.end_time - self.metrics.start_time).total_seconds()
        self.updated_at = datetime.now()
        logger.info(f"Completed execution: {self.execution_id} ({self.tool_name}) - Success: {result.success}")
        
    def fail_execution(self, error: str, error_type: str = "unknown"):
        """Mark execution as failed with error"""
        self.status = ExecutionStatus.FAILED
        self.result = ExecutionResult(success=False, error=error, error_type=error_type)
        self.metrics.end_time = datetime.now()
        if self.metrics.start_time:
            self.metrics.duration = (self.metrics.end_time - self.metrics.start_time).total_seconds()
        self.updated_at = datetime.now()
        logger.error(f"Failed execution: {self.execution_id} ({self.tool_name}) - Error: {error}")
        
    def start_retry(self):
        """Mark execution as retrying"""
        self.status = ExecutionStatus.RETRYING
        self.metrics.retry_count += 1
        self.updated_at = datetime.now()
        logger.info(f"Retrying execution: {self.execution_id} ({self.tool_name}) - Attempt: {self.metrics.retry_count}")
        
    def cancel_execution(self):
        """Mark execution as cancelled"""
        self.status = ExecutionStatus.CANCELLED
        self.metrics.end_time = datetime.now()
        if self.metrics.start_time:
            self.metrics.duration = (self.metrics.end_time - self.metrics.start_time).total_seconds()
        self.updated_at = datetime.now()
        logger.info(f"Cancelled execution: {self.execution_id} ({self.tool_name})")
        
    def add_dependency(self, dependency_id: str):
        """Add a dependency to this execution"""
        self.dependencies.add(dependency_id)
        
    def add_dependent(self, dependent_id: str):
        """Add a dependent to this execution"""
        self.dependents.add(dependent_id)
        
    def is_ready_to_execute(self, completed_executions: Set[str]) -> bool:
        """Check if all dependencies are completed"""
        return self.dependencies.issubset(completed_executions)
        
    def is_terminal_state(self) -> bool:
        """Check if execution is in a terminal state"""
        return self.status in [ExecutionStatus.COMPLETED, ExecutionStatus.FAILED, ExecutionStatus.CANCELLED]
        
    def get_execution_time(self) -> Optional[float]:
        """Get execution time in seconds"""
        return self.metrics.duration
        
    def add_tag(self, tag: str):
        """Add a tag to this execution"""
        self.tags.add(tag)
        
    def has_tag(self, tag: str) -> bool:
        """Check if execution has a specific tag"""
        return tag in self.tags
        
    def set_metadata(self, key: str, value: Any):
        """Set metadata for this execution"""
        self.metadata[key] = value
        
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata for this execution"""
        return self.metadata.get(key, default)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert execution state to dictionary for serialization"""
        return {
            "execution_id": self.execution_id,
            "tool_name": self.tool_name,
            "params": self.params,
            "status": self.status.value,
            "result": self.result.to_dict() if self.result else None,
            "metrics": self.metrics.to_dict(),
            "dependencies": list(self.dependencies),
            "dependents": list(self.dependents),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": list(self.tags),
            "metadata": self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExecutionState':
        """Create execution state from dictionary"""
        state = cls(
            execution_id=data["execution_id"],
            tool_name=data["tool_name"],
            params=data["params"]
        )
        
        state.status = ExecutionStatus(data["status"])
        state.dependencies = set(data["dependencies"])
        state.dependents = set(data["dependents"])
        state.created_at = datetime.fromisoformat(data["created_at"])
        state.updated_at = datetime.fromisoformat(data["updated_at"])
        state.tags = set(data["tags"])
        state.metadata = data["metadata"]
        
        # Restore metrics
        metrics_data = data["metrics"]
        state.metrics.start_time = datetime.fromisoformat(metrics_data["start_time"]) if metrics_data["start_time"] else None
        state.metrics.end_time = datetime.fromisoformat(metrics_data["end_time"]) if metrics_data["end_time"] else None
        state.metrics.duration = metrics_data["duration"]
        state.metrics.retry_count = metrics_data["retry_count"]
        state.metrics.memory_usage = metrics_data["memory_usage"]
        
        # Restore result
        if data["result"]:
            result_data = data["result"]
            state.result = ExecutionResult(
                success=result_data["success"],
                data=result_data["data"],
                error=result_data["error"],
                error_type=result_data["error_type"],
                output=result_data["output"]
            )
            
        return state


class ExecutionStateManager:
    """
    Manages multiple execution states and provides querying capabilities
    """
    
    def __init__(self):
        self.executions: Dict[str, ExecutionState] = {}
        self.execution_order: List[str] = []
        
    def add_execution(self, execution: ExecutionState):
        """Add an execution to the manager"""
        self.executions[execution.execution_id] = execution
        self.execution_order.append(execution.execution_id)
        
    def get_execution(self, execution_id: str) -> Optional[ExecutionState]:
        """Get an execution by ID"""
        return self.executions.get(execution_id)
        
    def get_executions_by_status(self, status: ExecutionStatus) -> List[ExecutionState]:
        """Get all executions with a specific status"""
        return [exec for exec in self.executions.values() if exec.status == status]
        
    def get_ready_executions(self) -> List[ExecutionState]:
        """Get executions that are ready to run (all dependencies completed)"""
        completed_ids = {exec_id for exec_id, exec in self.executions.items() 
                        if exec.status == ExecutionStatus.COMPLETED}
        
        return [exec for exec in self.executions.values() 
                if exec.status == ExecutionStatus.PENDING and exec.is_ready_to_execute(completed_ids)]
        
    def get_failed_executions(self) -> List[ExecutionState]:
        """Get all failed executions"""
        return self.get_executions_by_status(ExecutionStatus.FAILED)
        
    def get_completed_executions(self) -> List[ExecutionState]:
        """Get all completed executions"""
        return self.get_executions_by_status(ExecutionStatus.COMPLETED)
        
    def is_all_completed(self) -> bool:
        """Check if all executions are completed"""
        return all(exec.is_terminal_state() for exec in self.executions.values())
        
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of all executions"""
        status_counts = {}
        for status in ExecutionStatus:
            status_counts[status.value] = len(self.get_executions_by_status(status))
            
        total_duration = sum(exec.get_execution_time() or 0 for exec in self.executions.values())
        
        return {
            "total_executions": len(self.executions),
            "status_counts": status_counts,
            "total_duration": total_duration,
            "execution_order": self.execution_order.copy()
        }
        
    def clear(self):
        """Clear all executions"""
        self.executions.clear()
        self.execution_order.clear()

    def to_dict(self) -> Dict[str, Any]:
        """Convert all executions to dictionary for serialization"""
        return {
            "executions": {exec_id: exec.to_dict() for exec_id, exec in self.executions.items()},
            "execution_order": self.execution_order.copy()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExecutionStateManager':
        """Create execution state manager from dictionary"""
        manager = cls()
        manager.execution_order = data["execution_order"].copy()

        for exec_id, exec_data in data["executions"].items():
            execution = ExecutionState.from_dict(exec_data)
            manager.executions[exec_id] = execution

        return manager
