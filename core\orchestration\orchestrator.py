"""
Tool Execution Orchestrator

This module provides the main orchestration engine for multi-step tool execution:
- Coordinates execution of multiple tool calls
- Manages dependencies and sequencing
- Handles errors and retries
- Provides progress reporting
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable, Awaitable

from .execution_plan import ExecutionPlan, ExecutionStep, ExecutionMode
from .execution_state import ExecutionState, ExecutionStatus, ExecutionResult, ExecutionStateManager
from .error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, RetryConfig
from .progress_reporter import ProgressReporter
from .request_analyzer import RequestAnalyzer
from .dynamic_planner import DynamicPlanner

logger = logging.getLogger(__name__)


class ToolOrchestrator:
    """
    Main orchestration engine for multi-step tool execution
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.state_manager = ExecutionStateManager()
        self.error_handler = ErrorHandler()
        self.progress_reporter = ProgressReporter()
        self.request_analyzer = RequestAnalyzer()
        self.dynamic_planner = DynamicPlanner()

        # Tool execution functions
        self.tool_executors: Dict[str, Callable] = {}

        # Configuration
        self.max_parallel_executions = self.config.get("max_parallel_executions", 5)
        self.default_timeout = self.config.get("default_timeout", 60.0)
        self.enable_optimization = self.config.get("enable_optimization", True)
        self.enable_dynamic_planning = self.config.get("enable_dynamic_planning", True)

        # State
        self.current_plan: Optional[ExecutionPlan] = None
        self.is_executing = False

        # Initialize dynamic planning handlers
        self._initialize_dynamic_planning()
        
    def register_tool_executor(self, tool_name: str, executor: Callable):
        """Register a tool executor function"""
        self.tool_executors[tool_name] = executor
        logger.info(f"Registered tool executor: {tool_name}")
        
    def set_retry_config(self, tool_name: str, config: RetryConfig):
        """Set retry configuration for a specific tool"""
        self.error_handler.set_tool_config(tool_name, config)
        
    def add_progress_handler(self, handler: Callable):
        """Add a progress event handler"""
        self.progress_reporter.add_event_handler(handler)
        
    async def analyze_and_execute(self, user_input: str, tool_calls: List[Any] = None) -> Dict[str, Any]:
        """Analyze user request and execute appropriate tool sequence"""
        try:
            # Analyze the request
            analysis = self.request_analyzer.analyze_request(user_input)
            
            if analysis["is_multi_step"]:
                # Create execution plan
                plan = self.request_analyzer.create_execution_plan(user_input, analysis)
                if plan:
                    # Execute the plan
                    result = await self.execute_plan(plan)
                    result["analysis"] = analysis
                    return result
                    
            # Fall back to single tool execution if provided
            if tool_calls:
                return await self.execute_tool_calls(tool_calls)
                
            # No multi-step execution needed
            return {
                "success": True,
                "is_multi_step": False,
                "analysis": analysis,
                "message": "Single operation - no orchestration needed"
            }
            
        except Exception as e:
            logger.error(f"Error in analyze_and_execute: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis": analysis if 'analysis' in locals() else None
            }
            
    async def execute_plan(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """Execute a complete execution plan"""
        if self.is_executing:
            return {"success": False, "error": "Another execution is already in progress"}
            
        self.is_executing = True
        self.current_plan = plan
        
        try:
            # Validate the plan
            is_valid, errors = plan.validate_plan()
            if not is_valid:
                return {"success": False, "error": f"Invalid plan: {errors}"}
                
            # Optimize the plan if enabled
            if self.enable_optimization:
                plan.optimize_plan()
                
            # Estimate duration
            tool_durations = self._get_tool_duration_estimates()
            estimated_duration = plan.estimate_duration(tool_durations)
            
            # Initialize progress tracking
            self.progress_reporter.start_plan(len(plan), estimated_duration)
            
            # Create execution states for all steps
            for step in plan:
                execution_state = ExecutionState(
                    execution_id=step.step_id,
                    tool_name=step.tool_name,
                    params=step.params
                )
                
                # Add dependencies
                for dep_id in step.dependencies:
                    execution_state.add_dependency(dep_id)
                    
                self.state_manager.add_execution(execution_state)
                
            # Execute the plan
            result = await self._execute_plan_steps(plan)
            
            return {
                "success": result["success"],
                "plan_id": plan.plan_id,
                "executed_steps": result["executed_steps"],
                "failed_steps": result["failed_steps"],
                "total_duration": result["total_duration"],
                "results": result["results"],
                "error": result.get("error")
            }
            
        except Exception as e:
            logger.error(f"Error executing plan: {e}")
            return {"success": False, "error": str(e)}
        finally:
            self.is_executing = False
            self.current_plan = None
            
    async def _execute_plan_steps(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """Execute all steps in the plan"""
        start_time = datetime.now()
        executed_steps = []
        failed_steps = []
        results = {}
        
        try:
            # Get execution groups (parallel/sequential)
            execution_groups = plan.get_parallel_groups()
            
            for group in execution_groups:
                if len(group) == 1:
                    # Sequential execution
                    step = group[0]
                    result = await self._execute_single_step(step)
                    
                    if result["success"]:
                        executed_steps.append(step.step_id)
                        results[step.step_id] = result
                    else:
                        failed_steps.append(step.step_id)
                        results[step.step_id] = result
                        
                        # Enhanced error handling - try to recover or continue
                        if not self._should_continue_after_failure(step, result):
                            break
                else:
                    # Parallel execution with batching optimization
                    batch_groups = self._group_steps_for_batching(group)

                    for batch_group in batch_groups:
                        if len(batch_group) == 1:
                            # Single step execution
                            step = batch_group[0]
                            result = await self._execute_single_step(step)

                            if result["success"]:
                                executed_steps.append(step.step_id)
                            else:
                                failed_steps.append(step.step_id)
                            results[step.step_id] = result
                        else:
                            # Batch execution
                            batch_result = await self._execute_batch(batch_group)

                            for step_id, result in batch_result.items():
                                if result["success"]:
                                    executed_steps.append(step_id)
                                else:
                                    failed_steps.append(step_id)
                                results[step_id] = result
                            
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            
            return {
                "success": len(failed_steps) == 0,
                "executed_steps": executed_steps,
                "failed_steps": failed_steps,
                "total_duration": total_duration,
                "results": results,
                "error": f"Failed steps: {failed_steps}" if failed_steps else None
            }
            
        except Exception as e:
            logger.error(f"Error in plan execution: {e}")
            return {
                "success": False,
                "executed_steps": executed_steps,
                "failed_steps": failed_steps,
                "total_duration": (datetime.now() - start_time).total_seconds(),
                "results": results,
                "error": str(e)
            }
            
    async def _execute_single_step(self, step: ExecutionStep) -> Dict[str, Any]:
        """Execute a single step with error handling and retry logic"""
        execution_state = self.state_manager.get_execution(step.step_id)
        if not execution_state:
            return {"success": False, "error": f"Execution state not found for {step.step_id}"}
            
        # Check if tool executor exists
        if step.tool_name not in self.tool_executors:
            error_msg = f"No executor registered for tool: {step.tool_name}"
            execution_state.fail_execution(error_msg, "configuration_error")
            return {"success": False, "error": error_msg}
            
        # Start progress tracking
        self.progress_reporter.start_execution(step.step_id, step.tool_name)
        
        try:
            # Execute with retry logic
            async def execute_operation():
                execution_state.start_execution()
                
                # Get tool executor
                executor = self.tool_executors[step.tool_name]
                
                # Execute the tool
                if asyncio.iscoroutinefunction(executor):
                    result = await executor(**step.params)
                else:
                    result = executor(**step.params)
                    
                return result
                
            # Execute with error handling
            result = await self.error_handler.execute_with_retry(
                execution_id=step.step_id,
                tool_name=step.tool_name,
                operation=execute_operation
            )
            
            # Mark as completed
            execution_result = ExecutionResult(success=True, data=result)
            execution_state.complete_execution(execution_result)
            self.progress_reporter.complete_execution(step.step_id, success=True)

            # Apply dynamic planning if enabled
            if self.enable_dynamic_planning and self.current_plan:
                try:
                    modifications = self.dynamic_planner.adapt_plan_based_on_result(
                        self.current_plan, step.step_id, result
                    )
                    if modifications:
                        self.dynamic_planner.apply_modifications(self.current_plan, modifications)
                        logger.info(f"Applied {len(modifications)} dynamic plan modifications")
                except Exception as e:
                    logger.error(f"Error in dynamic planning: {e}")

            return {"success": True, "result": result}
            
        except Exception as e:
            # Mark as failed
            execution_state.fail_execution(str(e), type(e).__name__)
            self.progress_reporter.complete_execution(step.step_id, success=False)
            
            logger.error(f"Step {step.step_id} failed: {e}")
            return {"success": False, "error": str(e)}

    def _group_steps_for_batching(self, steps: List[ExecutionStep]) -> List[List[ExecutionStep]]:
        """Group steps for batch execution based on tags and tool types"""
        batch_groups = []
        processed_steps = set()

        # Group by batch tags
        tag_groups = {}
        for step in steps:
            if step.step_id in processed_steps:
                continue

            # Find batch tags
            batch_tags = [tag for tag in step.tags if tag.startswith("batch_")]

            if batch_tags:
                # Use the first batch tag for grouping
                batch_tag = batch_tags[0]
                if batch_tag not in tag_groups:
                    tag_groups[batch_tag] = []
                tag_groups[batch_tag].append(step)
                processed_steps.add(step.step_id)

        # Create batch groups from tag groups
        for tag, tag_steps in tag_groups.items():
            if len(tag_steps) > 1 and self._can_batch_steps(tag_steps):
                batch_groups.append(tag_steps)
                logger.info(f"Created batch group for {tag}: {len(tag_steps)} steps")
            else:
                # Add as individual steps
                for step in tag_steps:
                    batch_groups.append([step])

        # Add remaining steps as individual executions
        for step in steps:
            if step.step_id not in processed_steps:
                batch_groups.append([step])

        return batch_groups

    def _can_batch_steps(self, steps: List[ExecutionStep]) -> bool:
        """Check if steps can be batched together"""
        if not steps:
            return False

        # All steps must have the same tool
        tool_names = {step.tool_name for step in steps}
        if len(tool_names) != 1:
            return False

        tool_name = list(tool_names)[0]

        # Only certain tools support batching
        batchable_tools = ["read_file", "list_files", "search_files", "create_directory"]
        return tool_name in batchable_tools

    async def _execute_batch(self, steps: List[ExecutionStep]) -> Dict[str, Dict[str, Any]]:
        """Execute a batch of similar steps"""
        if not steps:
            return {}

        tool_name = steps[0].tool_name
        logger.info(f"Executing batch of {len(steps)} {tool_name} operations")

        # Execute all steps in parallel
        tasks = []
        for step in steps:
            task = asyncio.create_task(self._execute_single_step(step))
            tasks.append((step.step_id, task))

        # Wait for all tasks with timeout
        results = {}
        timeout = self.config.get("batch_timeout", 120.0)  # 2 minutes default

        try:
            completed_tasks = await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=timeout
            )

            for (step_id, _), result in zip(tasks, completed_tasks):
                if isinstance(result, Exception):
                    results[step_id] = {"success": False, "error": str(result)}
                else:
                    results[step_id] = result

        except asyncio.TimeoutError:
            logger.error(f"Batch execution timed out after {timeout} seconds")
            for step_id, task in tasks:
                if not task.done():
                    task.cancel()
                    results[step_id] = {"success": False, "error": "Batch execution timeout"}
                else:
                    try:
                        results[step_id] = task.result()
                    except Exception as e:
                        results[step_id] = {"success": False, "error": str(e)}

        return results

    async def execute_tool_calls(self, tool_calls: List[Any]) -> Dict[str, Any]:
        """Execute a list of tool calls sequentially"""
        if self.is_executing:
            return {"success": False, "error": "Another execution is already in progress"}
            
        self.is_executing = True
        
        try:
            results = []
            
            for i, tool_call in enumerate(tool_calls):
                execution_id = f"tool_call_{i}_{uuid.uuid4().hex[:8]}"
                
                # Start progress tracking
                self.progress_reporter.start_execution(execution_id, tool_call.name)
                
                try:
                    # Execute the tool call
                    if tool_call.name in self.tool_executors:
                        executor = self.tool_executors[tool_call.name]
                        
                        if asyncio.iscoroutinefunction(executor):
                            result = await executor(**tool_call.params)
                        else:
                            result = executor(**tool_call.params)
                            
                        results.append({"success": True, "tool": tool_call.name, "result": result})
                        self.progress_reporter.complete_execution(execution_id, success=True)
                    else:
                        error_msg = f"No executor for tool: {tool_call.name}"
                        results.append({"success": False, "tool": tool_call.name, "error": error_msg})
                        self.progress_reporter.complete_execution(execution_id, success=False)
                        
                except Exception as e:
                    results.append({"success": False, "tool": tool_call.name, "error": str(e)})
                    self.progress_reporter.complete_execution(execution_id, success=False)
                    
            return {
                "success": all(r["success"] for r in results),
                "results": results
            }
            
        finally:
            self.is_executing = False
            
    def _get_tool_duration_estimates(self) -> Dict[str, float]:
        """Get estimated duration for each tool"""
        # Default estimates in seconds
        return {
            "read_file": 0.5,
            "write_to_file": 1.0,
            "replace_in_file": 1.5,
            "list_files": 0.3,
            "search_files": 2.0,
            "execute_command": 5.0,
            "create_directory": 0.2,
            "ask_followup_question": 10.0
        }
        
    def get_execution_status(self) -> Dict[str, Any]:
        """Get current execution status"""
        return {
            "is_executing": self.is_executing,
            "current_plan": self.current_plan.plan_id if self.current_plan else None,
            "progress": self.progress_reporter.get_progress_summary(),
            "state_summary": self.state_manager.get_execution_summary(),
            "error_summary": self.error_handler.get_error_summary()
        }
        
    def cancel_execution(self):
        """Cancel current execution"""
        if self.is_executing and self.current_plan:
            # Cancel all pending executions
            for execution in self.state_manager.get_executions_by_status(ExecutionStatus.PENDING):
                execution.cancel_execution()
                
            logger.info(f"Cancelled execution of plan: {self.current_plan.plan_id}")
            self.is_executing = False
            
    def clear_state(self):
        """Clear all execution state"""
        self.state_manager.clear()
        self.progress_reporter.clear()
        self.error_handler.clear_history()
        self.current_plan = None
        self.is_executing = False

    def _initialize_dynamic_planning(self):
        """Initialize dynamic planning handlers"""
        if not self.enable_dynamic_planning:
            return

        # Register result handlers for common tools
        self.dynamic_planner.register_result_handler("list_files", self._handle_list_files_result)
        self.dynamic_planner.register_result_handler("search_files", self._handle_search_files_result)
        self.dynamic_planner.register_result_handler("read_file", self._handle_read_file_result)

        # Register condition evaluators
        self.dynamic_planner.register_condition_evaluator("file_count_threshold", self._evaluate_file_count_condition)
        self.dynamic_planner.register_condition_evaluator("content_analysis", self._evaluate_content_analysis_condition)

    def _handle_list_files_result(self, plan: ExecutionPlan, step: ExecutionStep, result: Dict[str, Any]) -> List:
        """Handle list_files result for dynamic planning"""
        from .dynamic_planner import PlanModification, PlanModificationType

        modifications = []
        if result.get("success") and result.get("files"):
            files = result.get("files", [])

            # If we found many files, suggest batching
            if len(files) > 10:
                # Add a batch read operation instead of individual reads
                batch_step = ExecutionStep(
                    step_id=f"batch_read_{uuid.uuid4().hex[:8]}",
                    tool_name="read_file",
                    params={"path": "BATCH_FILES", "files": files[:10]},  # Limit to first 10
                    dependencies={step.step_id},
                    mode=ExecutionMode.PARALLEL,
                    tags={"auto_generated", "batch_read", "batch_files"}
                )

                modifications.append(PlanModification(
                    modification_type=PlanModificationType.ADD_STEP,
                    new_step=batch_step,
                    reason=f"Auto-generated batch read for {len(files)} files (limited to 10)"
                ))

        return modifications

    def _handle_search_files_result(self, plan: ExecutionPlan, step: ExecutionStep, result: Dict[str, Any]) -> List:
        """Handle search_files result for dynamic planning"""
        from .dynamic_planner import PlanModification, PlanModificationType

        modifications = []
        if result.get("success") and result.get("matches"):
            matches = result.get("matches", [])

            # Extract unique file paths
            file_paths = list(set(match.get("file", "") for match in matches if match.get("file")))

            if file_paths:
                # Create read steps for matching files
                for file_path in file_paths[:5]:  # Limit to first 5 matches
                    read_step = ExecutionStep(
                        step_id=f"read_match_{uuid.uuid4().hex[:8]}",
                        tool_name="read_file",
                        params={"path": file_path},
                        dependencies={step.step_id},
                        mode=ExecutionMode.PARALLEL,
                        tags={"auto_generated", "read_search_match"}
                    )

                    modifications.append(PlanModification(
                        modification_type=PlanModificationType.ADD_STEP,
                        new_step=read_step,
                        reason=f"Auto-generated read for search match: {file_path}"
                    ))

        return modifications

    def _handle_read_file_result(self, plan: ExecutionPlan, step: ExecutionStep, result: Dict[str, Any]) -> List:
        """Handle read_file result for dynamic planning"""
        from .dynamic_planner import PlanModification, PlanModificationType

        modifications = []
        if result.get("success") and result.get("content"):
            content = result.get("content", "")
            file_path = step.params.get("path", "")

            # Check for Python files with main blocks
            if file_path.endswith(".py") and "if __name__ == '__main__':" in content:
                run_step = ExecutionStep(
                    step_id=f"run_python_{uuid.uuid4().hex[:8]}",
                    tool_name="execute_command",
                    params={"command": f"python {file_path}"},
                    dependencies={step.step_id},
                    mode=ExecutionMode.SEQUENTIAL,
                    tags={"auto_generated", "python_execution"}
                )

                modifications.append(PlanModification(
                    modification_type=PlanModificationType.ADD_STEP,
                    new_step=run_step,
                    reason=f"Auto-generated Python execution for {file_path}"
                ))

        return modifications

    def _evaluate_file_count_condition(self, plan: ExecutionPlan, context: Dict[str, Any]) -> List:
        """Evaluate file count conditions"""
        # This is a placeholder for more complex condition evaluation
        return []

    def _evaluate_content_analysis_condition(self, plan: ExecutionPlan, context: Dict[str, Any]) -> List:
        """Evaluate content analysis conditions"""
        # This is a placeholder for more complex condition evaluation
        return []

    def _should_continue_after_failure(self, step: ExecutionStep, result: Dict[str, Any]) -> bool:
        """Determine if execution should continue after a step failure"""
        # Basic continue on failure setting
        if self.config.get("continue_on_failure", False):
            return True

        # Check if the failed step is marked as optional
        if step.has_tag("optional"):
            logger.info(f"Continuing after optional step failure: {step.step_id}")
            return True

        # Check if the failure is recoverable
        error_msg = result.get("error", "").lower()
        recoverable_errors = [
            "file not found",
            "permission denied",
            "timeout",
            "network error"
        ]

        for recoverable_error in recoverable_errors:
            if recoverable_error in error_msg:
                # Try to create a recovery step
                if self._attempt_error_recovery(step, result):
                    return True

        # Check if there are alternative steps that can be executed
        if self._has_alternative_steps(step):
            logger.info(f"Continuing with alternative steps after failure: {step.step_id}")
            return True

        return False

    def _attempt_error_recovery(self, failed_step: ExecutionStep, result: Dict[str, Any]) -> bool:
        """Attempt to recover from a step failure"""
        error_msg = result.get("error", "").lower()

        # File not found recovery
        if "file not found" in error_msg and failed_step.tool_name == "read_file":
            file_path = failed_step.params.get("path", "")
            if file_path:
                # Try to create the file if it's a reasonable operation
                if self._should_create_missing_file(file_path):
                    recovery_step = ExecutionStep(
                        step_id=f"recovery_create_{uuid.uuid4().hex[:8]}",
                        tool_name="write_to_file",
                        params={"path": file_path, "content": "# Auto-created file\n"},
                        dependencies=set(),
                        mode=ExecutionMode.SEQUENTIAL,
                        tags={"auto_generated", "error_recovery"}
                    )

                    if self.current_plan:
                        self.current_plan.add_step(recovery_step)
                        logger.info(f"Added recovery step to create missing file: {file_path}")
                        return True

        # Permission denied recovery
        elif "permission denied" in error_msg:
            # Log the issue but continue with other steps
            logger.warning(f"Permission denied for step {failed_step.step_id}, marking as skipped")
            return True

        return False

    def _should_create_missing_file(self, file_path: str) -> bool:
        """Determine if a missing file should be auto-created"""
        # Only create files with safe extensions
        safe_extensions = [".txt", ".md", ".py", ".json", ".yaml", ".yml"]
        return any(file_path.endswith(ext) for ext in safe_extensions)

    def _has_alternative_steps(self, failed_step: ExecutionStep) -> bool:
        """Check if there are alternative steps that can achieve similar results"""
        if not self.current_plan:
            return False

        # Look for steps with similar functionality
        similar_tools = {
            "read_file": ["list_files"],
            "search_files": ["list_files"],
            "execute_command": ["ask_followup_question"]
        }

        alternative_tools = similar_tools.get(failed_step.tool_name, [])

        # Check if any alternative tools are available in the plan
        for step in self.current_plan:
            if step.tool_name in alternative_tools and step.step_id != failed_step.step_id:
                return True

        return False
