"""
Progress Reporting System

This module provides real-time progress reporting for multi-step operations:
- Progress tracking and estimation
- Real-time status updates
- Performance metrics
- Completion time estimation
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from .execution_state import ExecutionState, ExecutionStatus

logger = logging.getLogger(__name__)


class ProgressEventType(Enum):
    """Types of progress events"""
    PLAN_STARTED = "plan_started"
    STEP_STARTED = "step_started"
    STEP_PROGRESS = "step_progress"
    STEP_COMPLETED = "step_completed"
    STEP_FAILED = "step_failed"
    STEP_RETRYING = "step_retrying"
    PLAN_COMPLETED = "plan_completed"
    PLAN_FAILED = "plan_failed"


@dataclass
class ProgressEvent:
    """Progress event data"""
    event_type: ProgressEventType
    timestamp: datetime
    execution_id: str
    tool_name: str
    message: str
    progress_percent: float = 0.0
    estimated_remaining: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "execution_id": self.execution_id,
            "tool_name": self.tool_name,
            "message": self.message,
            "progress_percent": self.progress_percent,
            "estimated_remaining": self.estimated_remaining,
            "metadata": self.metadata
        }


class ProgressTracker:
    """
    Tracks progress for individual executions
    """
    
    def __init__(self, execution_id: str, tool_name: str, estimated_duration: Optional[float] = None):
        self.execution_id = execution_id
        self.tool_name = tool_name
        self.estimated_duration = estimated_duration
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.current_progress = 0.0
        self.status = ExecutionStatus.PENDING
        self.events: List[ProgressEvent] = []
        
    def start(self):
        """Mark tracking as started"""
        self.start_time = datetime.now()
        self.status = ExecutionStatus.EXECUTING
        self.current_progress = 0.0
        
        event = ProgressEvent(
            event_type=ProgressEventType.STEP_STARTED,
            timestamp=self.start_time,
            execution_id=self.execution_id,
            tool_name=self.tool_name,
            message=f"Started {self.tool_name}",
            progress_percent=0.0
        )
        self.events.append(event)
        
    def update_progress(self, progress_percent: float, message: Optional[str] = None):
        """Update progress percentage"""
        self.current_progress = max(0.0, min(100.0, progress_percent))
        
        event = ProgressEvent(
            event_type=ProgressEventType.STEP_PROGRESS,
            timestamp=datetime.now(),
            execution_id=self.execution_id,
            tool_name=self.tool_name,
            message=message or f"Progress: {self.current_progress:.1f}%",
            progress_percent=self.current_progress,
            estimated_remaining=self.get_estimated_remaining()
        )
        self.events.append(event)
        
    def complete(self, success: bool = True):
        """Mark tracking as completed"""
        self.end_time = datetime.now()
        self.status = ExecutionStatus.COMPLETED if success else ExecutionStatus.FAILED
        self.current_progress = 100.0 if success else self.current_progress
        
        event_type = ProgressEventType.STEP_COMPLETED if success else ProgressEventType.STEP_FAILED
        message = f"Completed {self.tool_name}" if success else f"Failed {self.tool_name}"
        
        event = ProgressEvent(
            event_type=event_type,
            timestamp=self.end_time,
            execution_id=self.execution_id,
            tool_name=self.tool_name,
            message=message,
            progress_percent=self.current_progress,
            metadata={"duration": self.get_duration()}
        )
        self.events.append(event)
        
    def retry(self, attempt_number: int):
        """Mark as retrying"""
        self.status = ExecutionStatus.RETRYING
        
        event = ProgressEvent(
            event_type=ProgressEventType.STEP_RETRYING,
            timestamp=datetime.now(),
            execution_id=self.execution_id,
            tool_name=self.tool_name,
            message=f"Retrying {self.tool_name} (attempt {attempt_number})",
            progress_percent=self.current_progress,
            metadata={"attempt_number": attempt_number}
        )
        self.events.append(event)
        
    def get_duration(self) -> Optional[float]:
        """Get execution duration in seconds"""
        if not self.start_time:
            return None
        end_time = self.end_time or datetime.now()
        return (end_time - self.start_time).total_seconds()
        
    def get_estimated_remaining(self) -> Optional[float]:
        """Get estimated remaining time in seconds"""
        if not self.start_time or self.current_progress <= 0:
            return self.estimated_duration
            
        elapsed = self.get_duration()
        if not elapsed:
            return self.estimated_duration
            
        # Calculate based on current progress
        estimated_total = elapsed / (self.current_progress / 100.0)
        remaining = estimated_total - elapsed
        
        return max(0.0, remaining)


class ProgressReporter:
    """
    Manages progress reporting for multiple executions
    """
    
    def __init__(self):
        self.trackers: Dict[str, ProgressTracker] = {}
        self.event_handlers: List[Callable[[ProgressEvent], None]] = []
        self.plan_start_time: Optional[datetime] = None
        self.plan_estimated_duration: Optional[float] = None
        self.total_steps = 0
        self.completed_steps = 0
        
    def add_event_handler(self, handler: Callable[[ProgressEvent], None]):
        """Add an event handler for progress events"""
        self.event_handlers.append(handler)
        
    def remove_event_handler(self, handler: Callable[[ProgressEvent], None]):
        """Remove an event handler"""
        if handler in self.event_handlers:
            self.event_handlers.remove(handler)
            
    def _emit_event(self, event: ProgressEvent):
        """Emit a progress event to all handlers"""
        for handler in self.event_handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"Error in progress event handler: {e}")
                
    def start_plan(self, total_steps: int, estimated_duration: Optional[float] = None):
        """Start tracking a new execution plan"""
        self.plan_start_time = datetime.now()
        self.plan_estimated_duration = estimated_duration
        self.total_steps = total_steps
        self.completed_steps = 0
        
        event = ProgressEvent(
            event_type=ProgressEventType.PLAN_STARTED,
            timestamp=self.plan_start_time,
            execution_id="plan",
            tool_name="orchestrator",
            message=f"Started execution plan with {total_steps} steps",
            progress_percent=0.0,
            estimated_remaining=estimated_duration,
            metadata={"total_steps": total_steps}
        )
        self._emit_event(event)
        
    def start_execution(self, execution_id: str, tool_name: str, estimated_duration: Optional[float] = None):
        """Start tracking a new execution"""
        tracker = ProgressTracker(execution_id, tool_name, estimated_duration)
        tracker.start()
        self.trackers[execution_id] = tracker
        
        # Emit the start event
        if tracker.events:
            self._emit_event(tracker.events[-1])
            
    def update_execution_progress(self, execution_id: str, progress_percent: float, message: Optional[str] = None):
        """Update progress for an execution"""
        tracker = self.trackers.get(execution_id)
        if not tracker:
            logger.warning(f"No tracker found for execution {execution_id}")
            return
            
        tracker.update_progress(progress_percent, message)
        
        # Emit the progress event
        if tracker.events:
            self._emit_event(tracker.events[-1])
            
    def complete_execution(self, execution_id: str, success: bool = True):
        """Mark an execution as completed"""
        tracker = self.trackers.get(execution_id)
        if not tracker:
            logger.warning(f"No tracker found for execution {execution_id}")
            return
            
        tracker.complete(success)
        
        if success:
            self.completed_steps += 1
            
        # Emit the completion event
        if tracker.events:
            self._emit_event(tracker.events[-1])
            
        # Check if plan is complete
        if self.completed_steps >= self.total_steps:
            self._complete_plan(success=True)
            
    def retry_execution(self, execution_id: str, attempt_number: int):
        """Mark an execution as retrying"""
        tracker = self.trackers.get(execution_id)
        if not tracker:
            logger.warning(f"No tracker found for execution {execution_id}")
            return
            
        tracker.retry(attempt_number)
        
        # Emit the retry event
        if tracker.events:
            self._emit_event(tracker.events[-1])
            
    def _complete_plan(self, success: bool = True):
        """Mark the entire plan as completed"""
        if not self.plan_start_time:
            return
            
        end_time = datetime.now()
        duration = (end_time - self.plan_start_time).total_seconds()
        
        event_type = ProgressEventType.PLAN_COMPLETED if success else ProgressEventType.PLAN_FAILED
        message = f"Plan completed in {duration:.2f}s" if success else "Plan failed"
        
        event = ProgressEvent(
            event_type=event_type,
            timestamp=end_time,
            execution_id="plan",
            tool_name="orchestrator",
            message=message,
            progress_percent=100.0 if success else self.get_overall_progress(),
            metadata={
                "duration": duration,
                "completed_steps": self.completed_steps,
                "total_steps": self.total_steps
            }
        )
        self._emit_event(event)
        
    def get_overall_progress(self) -> float:
        """Get overall progress percentage"""
        if self.total_steps == 0:
            return 0.0
            
        # Base progress on completed steps
        base_progress = (self.completed_steps / self.total_steps) * 100.0
        
        # Add partial progress from currently executing steps
        executing_trackers = [t for t in self.trackers.values() if t.status == ExecutionStatus.EXECUTING]
        if executing_trackers and self.total_steps > 0:
            partial_progress = sum(t.current_progress for t in executing_trackers) / len(executing_trackers)
            step_weight = 100.0 / self.total_steps
            base_progress += (partial_progress / 100.0) * step_weight
            
        return min(100.0, base_progress)
        
    def get_estimated_remaining_time(self) -> Optional[float]:
        """Get estimated remaining time for the entire plan"""
        if not self.plan_start_time:
            return self.plan_estimated_duration
            
        overall_progress = self.get_overall_progress()
        if overall_progress <= 0:
            return self.plan_estimated_duration
            
        elapsed = (datetime.now() - self.plan_start_time).total_seconds()
        estimated_total = elapsed / (overall_progress / 100.0)
        remaining = estimated_total - elapsed
        
        return max(0.0, remaining)
        
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get a summary of current progress"""
        return {
            "overall_progress": self.get_overall_progress(),
            "completed_steps": self.completed_steps,
            "total_steps": self.total_steps,
            "estimated_remaining": self.get_estimated_remaining_time(),
            "active_executions": len([t for t in self.trackers.values() if t.status == ExecutionStatus.EXECUTING]),
            "failed_executions": len([t for t in self.trackers.values() if t.status == ExecutionStatus.FAILED])
        }
        
    def get_execution_details(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific execution"""
        tracker = self.trackers.get(execution_id)
        if not tracker:
            return None
            
        return {
            "execution_id": execution_id,
            "tool_name": tracker.tool_name,
            "status": tracker.status.value,
            "progress": tracker.current_progress,
            "duration": tracker.get_duration(),
            "estimated_remaining": tracker.get_estimated_remaining(),
            "events": [event.to_dict() for event in tracker.events]
        }
        
    def clear(self):
        """Clear all tracking data"""
        self.trackers.clear()
        self.plan_start_time = None
        self.plan_estimated_duration = None
        self.total_steps = 0
        self.completed_steps = 0
