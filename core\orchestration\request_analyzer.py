"""
Request Analysis for Multi-Step Operations

This module analyzes user requests to determine if they require multiple tool calls:
- Pattern recognition for multi-step operations
- Automatic planning of tool sequences
- Dependency detection
- Request optimization
"""

import re
import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from .execution_plan import ExecutionPlan, ExecutionStep, ExecutionMode

logger = logging.getLogger(__name__)


class RequestType(Enum):
    """Types of requests that can be analyzed"""
    SINGLE_OPERATION = "single_operation"
    MULTI_FILE_READ = "multi_file_read"
    PROJECT_ANALYSIS = "project_analysis"
    FILE_CREATION_SEQUENCE = "file_creation_sequence"
    SEARCH_AND_PROCESS = "search_and_process"
    BATCH_OPERATION = "batch_operation"
    CONDITIONAL_OPERATION = "conditional_operation"


@dataclass
class RequestPattern:
    """Pattern for recognizing multi-step requests"""
    name: str
    keywords: List[str]
    request_type: RequestType
    tool_sequence: List[str]
    description: str
    confidence_threshold: float = 0.7


class RequestAnalyzer:
    """
    Analyzes user requests to determine multi-step operation requirements
    """
    
    def __init__(self):
        self.patterns = self._initialize_patterns()
        self.tool_dependencies = self._initialize_tool_dependencies()
        
    def _initialize_patterns(self) -> List[RequestPattern]:
        """Initialize request patterns for multi-step operations"""
        return [
            RequestPattern(
                name="check_project_files",
                keywords=["check", "analyze", "review", "examine", "project", "files", "all files", "workspace", "codebase"],
                request_type=RequestType.PROJECT_ANALYSIS,
                tool_sequence=["list_files", "read_file"],
                description="Check and analyze all files in the project",
                confidence_threshold=0.4
            ),
            RequestPattern(
                name="read_all_files",
                keywords=["read", "show", "display", "all", "files", "everything", "entire", "every file"],
                request_type=RequestType.MULTI_FILE_READ,
                tool_sequence=["list_files", "read_file"],
                description="Read all files in the workspace",
                confidence_threshold=0.5
            ),
            RequestPattern(
                name="search_and_analyze",
                keywords=["search", "find", "look for", "analyze", "pattern", "grep", "locate", "discover"],
                request_type=RequestType.SEARCH_AND_PROCESS,
                tool_sequence=["search_files", "read_file"],
                description="Search for patterns and analyze matching files",
                confidence_threshold=0.5
            ),
            RequestPattern(
                name="create_project_structure",
                keywords=["create", "setup", "initialize", "project", "structure", "scaffold", "build", "generate"],
                request_type=RequestType.FILE_CREATION_SEQUENCE,
                tool_sequence=["create_directory", "write_to_file"],
                description="Create a complete project structure",
                confidence_threshold=0.6
            ),
            RequestPattern(
                name="batch_file_operations",
                keywords=["batch", "multiple", "several", "many", "bulk", "mass", "all at once"],
                request_type=RequestType.BATCH_OPERATION,
                tool_sequence=["list_files", "read_file", "write_to_file"],
                description="Perform operations on multiple files",
                confidence_threshold=0.5
            ),
            RequestPattern(
                name="conditional_processing",
                keywords=["if", "when", "check if", "only if", "depending on", "conditional", "based on"],
                request_type=RequestType.CONDITIONAL_OPERATION,
                tool_sequence=["read_file", "write_to_file"],
                description="Perform operations based on conditions",
                confidence_threshold=0.6
            ),
            # New enhanced patterns
            RequestPattern(
                name="create_multiple_files",
                keywords=["create", "make", "generate", "multiple", "files", "several files", "many files"],
                request_type=RequestType.FILE_CREATION_SEQUENCE,
                tool_sequence=["create_directory", "write_to_file"],
                description="Create multiple files in sequence",
                confidence_threshold=0.5
            ),
            RequestPattern(
                name="modify_and_update",
                keywords=["modify", "update", "change", "edit", "replace", "and then", "followed by"],
                request_type=RequestType.BATCH_OPERATION,
                tool_sequence=["read_file", "replace_in_file"],
                description="Modify and update files in sequence",
                confidence_threshold=0.5
            ),
            RequestPattern(
                name="test_and_run",
                keywords=["test", "run", "execute", "and", "then", "after", "verify"],
                request_type=RequestType.BATCH_OPERATION,
                tool_sequence=["write_to_file", "execute_command"],
                description="Create files and then test or run them",
                confidence_threshold=0.6
            ),
            RequestPattern(
                name="backup_and_modify",
                keywords=["backup", "copy", "save", "before", "modify", "change", "update"],
                request_type=RequestType.BATCH_OPERATION,
                tool_sequence=["read_file", "write_to_file", "replace_in_file"],
                description="Backup files before modifying them",
                confidence_threshold=0.6
            )
        ]
        
    def _initialize_tool_dependencies(self) -> Dict[str, List[str]]:
        """Initialize tool dependency mappings"""
        return {
            "read_file": [],  # No dependencies
            "write_to_file": ["create_directory"],  # May need directory creation
            "replace_in_file": ["read_file"],  # Need to read first
            "list_files": [],  # No dependencies
            "search_files": [],  # No dependencies
            "execute_command": [],  # No dependencies
            "create_directory": [],  # No dependencies
            "ask_followup_question": []  # No dependencies
        }
        
    def analyze_request(self, user_input: str) -> Dict[str, Any]:
        """Analyze a user request to determine if multi-step execution is needed"""
        user_input_lower = user_input.lower()

        # Check for explicit multi-step indicators
        multi_step_indicators = [
            "and then", "after that", "next", "followed by", "also", "additionally",
            "first", "second", "third", "finally", "step by step", "one by one",
            "then", "afterwards", "subsequently", "later", "before", "after",
            "both", "all", "each", "every", "multiple", "several", "many"
        ]

        has_multi_step_indicators = any(indicator in user_input_lower for indicator in multi_step_indicators)

        # Check for numerical indicators (e.g., "2 files", "3 scripts")
        numerical_indicators = re.findall(r'\b(\d+)\s+(files?|scripts?|programs?|modules?|classes?|functions?)\b', user_input_lower)
        has_numerical_indicators = len(numerical_indicators) > 0 and any(int(num) > 1 for num, _ in numerical_indicators)

        # Check for action sequences (multiple verbs)
        action_verbs = ["create", "make", "write", "read", "modify", "update", "delete", "run", "execute", "test", "check", "analyze"]
        found_verbs = [verb for verb in action_verbs if verb in user_input_lower]
        has_multiple_actions = len(found_verbs) > 1

        # Match against patterns
        pattern_matches = []
        for pattern in self.patterns:
            confidence = self._calculate_pattern_confidence(user_input_lower, pattern)
            if confidence >= pattern.confidence_threshold:
                pattern_matches.append({
                    "pattern": pattern,
                    "confidence": confidence
                })

        # Sort by confidence
        pattern_matches.sort(key=lambda x: x["confidence"], reverse=True)

        # Enhanced multi-step detection
        is_multi_step = (
            has_multi_step_indicators or
            has_numerical_indicators or
            has_multiple_actions or
            len(pattern_matches) > 0 or
            self._detect_implicit_multi_step(user_input_lower)
        )

        result = {
            "is_multi_step": is_multi_step,
            "request_type": pattern_matches[0]["pattern"].request_type if pattern_matches else RequestType.SINGLE_OPERATION,
            "confidence": pattern_matches[0]["confidence"] if pattern_matches else 0.0,
            "matched_patterns": pattern_matches,
            "has_explicit_indicators": has_multi_step_indicators,
            "has_numerical_indicators": has_numerical_indicators,
            "has_multiple_actions": has_multiple_actions,
            "numerical_indicators": numerical_indicators,
            "found_verbs": found_verbs,
            "suggested_tools": self._suggest_tools(user_input_lower, pattern_matches),
            "estimated_complexity": self._estimate_complexity(user_input_lower, pattern_matches),
            "estimated_steps": self._estimate_step_count(user_input_lower, numerical_indicators, found_verbs)
        }

        return result
        
    def _calculate_pattern_confidence(self, user_input: str, pattern: RequestPattern) -> float:
        """Calculate confidence score for a pattern match"""
        keyword_matches = 0
        total_keywords = len(pattern.keywords)
        
        for keyword in pattern.keywords:
            if keyword in user_input:
                keyword_matches += 1
                
        # Base confidence on keyword matches
        base_confidence = keyword_matches / total_keywords if total_keywords > 0 else 0.0
        
        # Boost confidence for exact phrase matches
        phrase_boost = 0.0
        for keyword in pattern.keywords:
            if len(keyword.split()) > 1 and keyword in user_input:
                phrase_boost += 0.2
                
        # Boost confidence for request type specific words
        type_boost = 0.0
        if pattern.request_type == RequestType.PROJECT_ANALYSIS:
            if any(word in user_input for word in ["project", "workspace", "codebase"]):
                type_boost += 0.1
        elif pattern.request_type == RequestType.MULTI_FILE_READ:
            if any(word in user_input for word in ["all", "every", "entire"]):
                type_boost += 0.1
                
        return min(1.0, base_confidence + phrase_boost + type_boost)
        
    def _suggest_tools(self, user_input: str, pattern_matches: List[Dict]) -> List[str]:
        """Suggest tools based on request analysis"""
        if not pattern_matches:
            return self._analyze_single_operation(user_input)
            
        # Use the best matching pattern
        best_pattern = pattern_matches[0]["pattern"]
        return best_pattern.tool_sequence.copy()
        
    def _analyze_single_operation(self, user_input: str) -> List[str]:
        """Analyze single operation requests"""
        # Simple keyword-based tool suggestion
        if any(word in user_input for word in ["read", "show", "display", "view"]):
            return ["read_file"]
        elif any(word in user_input for word in ["write", "create", "make", "generate"]):
            return ["write_to_file"]
        elif any(word in user_input for word in ["search", "find", "grep", "look"]):
            return ["search_files"]
        elif any(word in user_input for word in ["list", "show files", "directory"]):
            return ["list_files"]
        elif any(word in user_input for word in ["replace", "modify", "change", "edit"]):
            return ["replace_in_file"]
        elif any(word in user_input for word in ["run", "execute", "command"]):
            return ["execute_command"]
        else:
            return ["ask_followup_question"]
            
    def _estimate_complexity(self, user_input: str, pattern_matches: List[Dict]) -> str:
        """Estimate the complexity of the request"""
        if not pattern_matches:
            return "low"
            
        best_pattern = pattern_matches[0]["pattern"]
        
        # Count potential file operations
        file_indicators = len(re.findall(r'\b(?:file|files)\b', user_input))
        
        if best_pattern.request_type in [RequestType.PROJECT_ANALYSIS, RequestType.BATCH_OPERATION]:
            return "high"
        elif best_pattern.request_type in [RequestType.MULTI_FILE_READ, RequestType.SEARCH_AND_PROCESS]:
            return "medium"
        elif file_indicators > 3:
            return "high"
        elif file_indicators > 1:
            return "medium"
        else:
            return "low"
            
    def create_execution_plan(self, user_input: str, analysis_result: Dict[str, Any]) -> Optional[ExecutionPlan]:
        """Create an execution plan based on request analysis"""
        if not analysis_result["is_multi_step"]:
            return None
            
        plan = ExecutionPlan(name=f"Multi-step: {user_input[:50]}...")
        suggested_tools = analysis_result["suggested_tools"]
        request_type = analysis_result["request_type"]
        
        if request_type == RequestType.PROJECT_ANALYSIS:
            return self._create_project_analysis_plan(plan, user_input)
        elif request_type == RequestType.MULTI_FILE_READ:
            return self._create_multi_file_read_plan(plan, user_input)
        elif request_type == RequestType.SEARCH_AND_PROCESS:
            return self._create_search_and_process_plan(plan, user_input)
        elif request_type == RequestType.FILE_CREATION_SEQUENCE:
            return self._create_file_creation_plan(plan, user_input)
        elif request_type == RequestType.BATCH_OPERATION:
            return self._create_batch_operation_plan(plan, user_input)
        else:
            return self._create_generic_plan(plan, suggested_tools)
            
    def _create_project_analysis_plan(self, plan: ExecutionPlan, user_input: str) -> ExecutionPlan:
        """Create plan for project analysis"""
        # Step 1: List all files
        list_step = ExecutionStep(
            step_id="list_files_step",
            tool_name="list_files",
            params={"path": ".", "recursive": "true"},
            dependencies=set(),
            mode=ExecutionMode.SEQUENTIAL
        )
        plan.add_step(list_step)
        
        # Step 2: Read files (will be dynamically created based on list results)
        read_step = ExecutionStep(
            step_id="read_files_step",
            tool_name="read_file",
            params={"path": "DYNAMIC"},  # Will be replaced with actual files
            dependencies={"list_files_step"},
            mode=ExecutionMode.PARALLEL
        )
        plan.add_step(read_step)
        
        return plan
        
    def _create_multi_file_read_plan(self, plan: ExecutionPlan, user_input: str) -> ExecutionPlan:
        """Create plan for reading multiple files"""
        return self._create_project_analysis_plan(plan, user_input)
        
    def _create_search_and_process_plan(self, plan: ExecutionPlan, user_input: str) -> ExecutionPlan:
        """Create plan for search and process operations"""
        # Extract search pattern from user input
        search_pattern = self._extract_search_pattern(user_input)
        
        # Step 1: Search for files
        search_step = ExecutionStep(
            step_id="search_files_step",
            tool_name="search_files",
            params={"path": ".", "regex": search_pattern, "file_pattern": "*"},
            dependencies=set(),
            mode=ExecutionMode.SEQUENTIAL
        )
        plan.add_step(search_step)
        
        # Step 2: Read matching files
        read_step = ExecutionStep(
            step_id="read_matching_files_step",
            tool_name="read_file",
            params={"path": "DYNAMIC"},
            dependencies={"search_files_step"},
            mode=ExecutionMode.PARALLEL
        )
        plan.add_step(read_step)
        
        return plan
        
    def _create_file_creation_plan(self, plan: ExecutionPlan, user_input: str) -> ExecutionPlan:
        """Create plan for file creation sequences"""
        # Step 1: Create directories
        dir_step = ExecutionStep(
            step_id="create_directories_step",
            tool_name="create_directory",
            params={"path": "DYNAMIC"},
            dependencies=set(),
            mode=ExecutionMode.SEQUENTIAL
        )
        plan.add_step(dir_step)
        
        # Step 2: Create files
        file_step = ExecutionStep(
            step_id="create_files_step",
            tool_name="write_to_file",
            params={"path": "DYNAMIC", "content": "DYNAMIC"},
            dependencies={"create_directories_step"},
            mode=ExecutionMode.SEQUENTIAL
        )
        plan.add_step(file_step)
        
        return plan
        
    def _create_batch_operation_plan(self, plan: ExecutionPlan, user_input: str) -> ExecutionPlan:
        """Create plan for batch operations"""
        return self._create_project_analysis_plan(plan, user_input)
        
    def _create_generic_plan(self, plan: ExecutionPlan, tools: List[str]) -> ExecutionPlan:
        """Create a generic plan from tool list"""
        previous_step_id = None
        
        for i, tool_name in enumerate(tools):
            step_id = f"{tool_name}_step_{i}"
            dependencies = {previous_step_id} if previous_step_id else set()
            
            step = ExecutionStep(
                step_id=step_id,
                tool_name=tool_name,
                params={"DYNAMIC": "DYNAMIC"},
                dependencies=dependencies,
                mode=ExecutionMode.SEQUENTIAL
            )
            plan.add_step(step)
            previous_step_id = step_id
            
        return plan
        
    def _extract_search_pattern(self, user_input: str) -> str:
        """Extract search pattern from user input"""
        # Look for quoted strings first
        quoted_matches = re.findall(r'"([^"]+)"', user_input)
        if quoted_matches:
            return quoted_matches[0]
            
        # Look for common search terms
        search_terms = ["search for", "find", "look for", "grep"]
        for term in search_terms:
            if term in user_input.lower():
                # Extract text after the search term
                parts = user_input.lower().split(term, 1)
                if len(parts) > 1:
                    remaining = parts[1].strip()
                    # Take first word or phrase
                    words = remaining.split()
                    if words:
                        return words[0]
                        
        return ".*"  # Default pattern

    def _detect_implicit_multi_step(self, user_input: str) -> bool:
        """Detect implicit multi-step operations"""
        # Check for project-wide operations
        project_indicators = ["project", "workspace", "codebase", "repository", "all files"]
        if any(indicator in user_input for indicator in project_indicators):
            return True

        # Check for complex operations that typically require multiple steps
        complex_operations = [
            "refactor", "restructure", "organize", "cleanup", "optimize",
            "migrate", "convert", "transform", "enhance", "improve"
        ]
        if any(op in user_input for op in complex_operations):
            return True

        # Check for operations on file types (implies multiple files)
        file_type_patterns = [
            r'\b\w+\s+files?\b',  # "python files", "text files"
            r'\ball\s+\w+\b',     # "all scripts", "all modules"
            r'\bevery\s+\w+\b'    # "every function", "every class"
        ]
        if any(re.search(pattern, user_input) for pattern in file_type_patterns):
            return True

        return False

    def _estimate_step_count(self, user_input: str, numerical_indicators: List[Tuple[str, str]], found_verbs: List[str]) -> int:
        """Estimate the number of steps required"""
        # Base step count
        step_count = 1

        # Add steps based on numerical indicators
        for num_str, _ in numerical_indicators:
            step_count += int(num_str) - 1

        # Add steps based on multiple actions
        if len(found_verbs) > 1:
            step_count += len(found_verbs) - 1

        # Add steps for complex operations
        if "project" in user_input or "workspace" in user_input:
            step_count += 2  # Usually list + process

        if "search" in user_input and ("read" in user_input or "analyze" in user_input):
            step_count += 1  # Search + process results

        return max(1, step_count)
