"""
Ignore Pattern Controller

This module implements ignore pattern handling similar to Cline's ClineIgnoreController:
- Supports .pyideignore files with gitignore syntax
- Validates file access based on ignore patterns
- Provides path filtering and validation
"""

import os
import logging
from pathlib import Path
from typing import List, Optional, Set
import fnmatch

# Optional imports with fallbacks
try:
    import pathspec
    HAS_PATHSPEC = True
except ImportError:
    HAS_PATHSPEC = False

logger = logging.getLogger(__name__)


class IgnoreController:
    """
    Controls file access by enforcing ignore patterns
    Replicates Cline's ClineIgnoreController functionality
    """
    
    def __init__(self, workspace_path: str, ignore_file: str = ".pyideignore"):
        self.workspace_path = Path(workspace_path).resolve()
        self.ignore_file = ignore_file
        self.ignore_spec = None
        self.ignore_content: Optional[str] = None
        self._load_ignore_patterns()
        
    def _load_ignore_patterns(self):
        """Load ignore patterns from .pyideignore file"""
        ignore_path = self.workspace_path / self.ignore_file
        
        try:
            if ignore_path.exists():
                with open(ignore_path, 'r', encoding='utf-8') as f:
                    self.ignore_content = f.read()
                    
                # Parse gitignore-style patterns
                patterns = []
                for line in self.ignore_content.splitlines():
                    line = line.strip()
                    if line and not line.startswith('#'):
                        patterns.append(line)
                        
                if patterns:
                    if HAS_PATHSPEC:
                        self.ignore_spec = pathspec.PathSpec.from_lines('gitwildmatch', patterns)
                        logger.info(f"Loaded {len(patterns)} ignore patterns from {ignore_path}")
                    else:
                        # Fallback: store patterns as simple list for fnmatch
                        self.ignore_spec = patterns
                        logger.info(f"Loaded {len(patterns)} ignore patterns (using fnmatch fallback)")
                else:
                    self.ignore_spec = None
            else:
                self.ignore_content = None
                self.ignore_spec = None
                logger.debug(f"No ignore file found at {ignore_path}")
                
        except Exception as e:
            logger.error(f"Error loading ignore patterns: {e}")
            self.ignore_content = None
            self.ignore_spec = None
            
    def validate_access(self, file_path: str) -> bool:
        """
        Check if a file should be accessible
        Returns True if file is accessible, False if ignored
        """
        # Always allow access if no ignore file exists
        if not self.ignore_content or not self.ignore_spec:
            return True
            
        try:
            # Convert to absolute path and then to relative path from workspace
            abs_path = Path(file_path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / file_path
                
            abs_path = abs_path.resolve()
            
            # Get relative path from workspace root
            try:
                rel_path = abs_path.relative_to(self.workspace_path)
            except ValueError:
                # Path is outside workspace - allow access (matches Cline behavior)
                logger.debug(f"Path outside workspace, allowing access: {file_path}")
                return True
                
            # Convert to POSIX path for consistent pattern matching
            posix_path = rel_path.as_posix()
            
            # Check if path matches any ignore patterns
            if HAS_PATHSPEC and hasattr(self.ignore_spec, 'match_file'):
                is_ignored = self.ignore_spec.match_file(posix_path)
            else:
                # Fallback: use fnmatch for simple pattern matching
                is_ignored = False
                if isinstance(self.ignore_spec, list):
                    for pattern in self.ignore_spec:
                        if fnmatch.fnmatch(posix_path, pattern) or fnmatch.fnmatch(posix_path, f"*/{pattern}"):
                            is_ignored = True
                            break
            
            logger.debug(f"Access check for {posix_path}: {'DENIED' if is_ignored else 'ALLOWED'}")
            return not is_ignored
            
        except Exception as e:
            logger.error(f"Error validating access for {file_path}: {e}")
            # Fail open for security - allow access on error
            return True
            
    def validate_command(self, command: str) -> Optional[str]:
        """
        Check if a terminal command should be allowed based on file access patterns
        Returns the path of file being accessed if it should be blocked, None if allowed
        """
        # Always allow if no ignore file exists
        if not self.ignore_content or not self.ignore_spec:
            return None
            
        # Commands that read/write files
        file_reading_commands = [
            'cat', 'less', 'more', 'head', 'tail', 'grep', 'awk', 'sed',
            'cp', 'mv', 'rm', 'chmod', 'chown', 'ln', 'touch',
            'python', 'python3', 'node', 'npm', 'pip', 'git'
        ]
        
        parts = command.split()
        if not parts:
            return None
            
        base_command = os.path.basename(parts[0])
        
        if base_command in file_reading_commands:
            # Check each argument that could be a file path
            for i in range(1, len(parts)):
                arg = parts[i]
                
                # Skip command flags/options
                if arg.startswith('-') or arg.startswith('/'):
                    continue
                    
                # Skip parameter assignments
                if '=' in arg:
                    continue
                    
                # Check if this looks like a file path
                if self._looks_like_file_path(arg):
                    if not self.validate_access(arg):
                        return arg
                        
        return None
        
    def _looks_like_file_path(self, arg: str) -> bool:
        """Check if an argument looks like a file path"""
        # Simple heuristics to identify file paths
        if not arg:
            return False
            
        # Contains path separators
        if '/' in arg or '\\' in arg:
            return True
            
        # Has file extension
        if '.' in arg and not arg.startswith('.'):
            return True
            
        # Looks like a relative path
        if arg.startswith('./') or arg.startswith('../'):
            return True
            
        return False
        
    def filter_paths(self, paths: List[str]) -> List[str]:
        """
        Filter a list of paths, removing those that should be ignored
        Returns list of allowed paths
        """
        try:
            allowed_paths = []
            for path in paths:
                if self.validate_access(path):
                    allowed_paths.append(path)
            return allowed_paths
        except Exception as e:
            logger.error(f"Error filtering paths: {e}")
            return []  # Fail closed for security
            
    def get_ignore_patterns(self) -> List[str]:
        """Get list of ignore patterns"""
        if not self.ignore_content:
            return []
            
        patterns = []
        for line in self.ignore_content.splitlines():
            line = line.strip()
            if line and not line.startswith('#'):
                patterns.append(line)
        return patterns
        
    def reload(self):
        """Reload ignore patterns from file"""
        self._load_ignore_patterns()
        
    def has_ignore_file(self) -> bool:
        """Check if ignore file exists"""
        return self.ignore_content is not None
