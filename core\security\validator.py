"""
Path and Security Validation

This module provides comprehensive validation for:
- Path validation and normalization
- Security checks and boundary enforcement
- File access control and validation
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import mimetypes

logger = logging.getLogger(__name__)


class PathValidator:
    """
    Path validation and normalization utilities
    """
    
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path).resolve()
        
    def normalize_path(self, path: str) -> Path:
        """Normalize a path to absolute path"""
        path_obj = Path(path)
        if not path_obj.is_absolute():
            path_obj = self.workspace_path / path
        return path_obj.resolve()
        
    def get_relative_path(self, path: str) -> Optional[Path]:
        """Get path relative to workspace, return None if outside workspace"""
        try:
            abs_path = self.normalize_path(path)
            return abs_path.relative_to(self.workspace_path)
        except ValueError:
            return None
            
    def is_in_workspace(self, path: str) -> bool:
        """Check if path is within workspace boundaries"""
        return self.get_relative_path(path) is not None
        
    def is_safe_path(self, path: str) -> bool:
        """Check if path is safe (no directory traversal, etc.)"""
        try:
            # Normalize the path
            normalized = self.normalize_path(path)
            
            # Check for directory traversal attempts
            path_parts = normalized.parts
            if '..' in path_parts:
                return False
                
            # Check for absolute paths that escape workspace
            if not self.is_in_workspace(str(normalized)):
                return False
                
            return True
            
        except Exception as e:
            logger.warning(f"Path safety check failed for {path}: {e}")
            return False
            
    def validate_filename(self, filename: str) -> bool:
        """Validate filename for safety"""
        if not filename or filename in ['.', '..']:
            return False
            
        # Check for invalid characters
        invalid_chars = '<>:"|?*\x00'
        if any(char in filename for char in invalid_chars):
            return False
            
        # Check for reserved names (Windows)
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        if filename.upper() in reserved_names:
            return False
            
        return True


class SecurityValidator:
    """
    Comprehensive security validation for file operations
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.security_config = config.get("security", {})
        self.max_file_size = self.security_config.get("max_file_size", 20 * 1024 * 1024)  # 20MB
        self.workspace_only = self.security_config.get("workspace_only", True)
        self.allowed_extensions = set(self.security_config.get("allowed_extensions", []))
        self.blocked_paths = set(self.security_config.get("blocked_paths", []))
        
    def validate_file_read(self, file_path: str, workspace_path: str) -> Dict[str, Any]:
        """
        Validate file read operation
        Returns validation result with success status and details
        """
        result = {
            "success": False,
            "error": None,
            "warnings": [],
            "file_info": {}
        }
        
        try:
            path_validator = PathValidator(workspace_path)
            abs_path = path_validator.normalize_path(file_path)
            
            # Check if file exists
            if not abs_path.exists():
                result["error"] = f"File not found: {file_path}"
                return result
                
            # Check if it's a file (not directory)
            if not abs_path.is_file():
                result["error"] = f"Path is not a file: {file_path}"
                return result
                
            # Check workspace boundaries
            if self.workspace_only and not path_validator.is_in_workspace(str(abs_path)):
                result["error"] = f"File outside workspace: {file_path}"
                return result
                
            # Check path safety
            if not path_validator.is_safe_path(str(abs_path)):
                result["error"] = f"Unsafe path: {file_path}"
                return result
                
            # Check file size
            file_size = abs_path.stat().st_size
            if file_size > self.max_file_size:
                result["error"] = f"File too large: {file_size} bytes (max: {self.max_file_size})"
                return result
                
            # Check file extension
            if self.allowed_extensions:
                file_ext = abs_path.suffix.lower()
                if file_ext not in self.allowed_extensions:
                    result["warnings"].append(f"File extension not in allowed list: {file_ext}")
                    
            # Check blocked paths
            rel_path = path_validator.get_relative_path(str(abs_path))
            if rel_path:
                path_str = str(rel_path)
                for blocked in self.blocked_paths:
                    if blocked in path_str:
                        result["error"] = f"Path contains blocked component: {blocked}"
                        return result
                        
            # Get file info
            result["file_info"] = {
                "size": file_size,
                "extension": abs_path.suffix,
                "mime_type": mimetypes.guess_type(str(abs_path))[0],
                "absolute_path": str(abs_path),
                "relative_path": str(rel_path) if rel_path else None
            }
            
            result["success"] = True
            return result
            
        except Exception as e:
            result["error"] = f"Validation error: {e}"
            return result
            
    def validate_file_write(self, file_path: str, content: str, workspace_path: str) -> Dict[str, Any]:
        """
        Validate file write operation
        """
        result = {
            "success": False,
            "error": None,
            "warnings": [],
            "operation_info": {}
        }
        
        try:
            path_validator = PathValidator(workspace_path)
            abs_path = path_validator.normalize_path(file_path)
            
            # Check workspace boundaries
            if self.workspace_only and not path_validator.is_in_workspace(str(abs_path)):
                result["error"] = f"File outside workspace: {file_path}"
                return result
                
            # Check path safety
            if not path_validator.is_safe_path(str(abs_path)):
                result["error"] = f"Unsafe path: {file_path}"
                return result
                
            # Check filename validity
            if not path_validator.validate_filename(abs_path.name):
                result["error"] = f"Invalid filename: {abs_path.name}"
                return result
                
            # Check content size
            content_size = len(content.encode('utf-8'))
            if content_size > self.max_file_size:
                result["error"] = f"Content too large: {content_size} bytes (max: {self.max_file_size})"
                return result
                
            # Check if file already exists
            file_exists = abs_path.exists()
            if file_exists and abs_path.is_dir():
                result["error"] = f"Path is a directory: {file_path}"
                return result
                
            # Check parent directory
            parent_dir = abs_path.parent
            if not parent_dir.exists():
                result["warnings"].append(f"Parent directory will be created: {parent_dir}")
                
            # Check file extension
            if self.allowed_extensions:
                file_ext = abs_path.suffix.lower()
                if file_ext not in self.allowed_extensions:
                    result["warnings"].append(f"File extension not in allowed list: {file_ext}")
                    
            # Check blocked paths
            rel_path = path_validator.get_relative_path(str(abs_path))
            if rel_path:
                path_str = str(rel_path)
                for blocked in self.blocked_paths:
                    if blocked in path_str:
                        result["error"] = f"Path contains blocked component: {blocked}"
                        return result
                        
            # Get operation info
            result["operation_info"] = {
                "file_exists": file_exists,
                "content_size": content_size,
                "absolute_path": str(abs_path),
                "relative_path": str(rel_path) if rel_path else None,
                "parent_exists": parent_dir.exists(),
                "operation_type": "overwrite" if file_exists else "create"
            }
            
            result["success"] = True
            return result
            
        except Exception as e:
            result["error"] = f"Validation error: {e}"
            return result
            
    def validate_command(self, command: str) -> Dict[str, Any]:
        """
        Validate command execution
        """
        result = {
            "success": False,
            "error": None,
            "warnings": [],
            "command_info": {}
        }
        
        try:
            if not command or not command.strip():
                result["error"] = "Empty command"
                return result
                
            # Basic command parsing
            parts = command.strip().split()
            if not parts:
                result["error"] = "Invalid command"
                return result
                
            base_command = parts[0]
            
            # Check for dangerous commands
            dangerous_commands = [
                'rm', 'del', 'format', 'fdisk', 'mkfs',
                'dd', 'shutdown', 'reboot', 'halt',
                'sudo', 'su', 'chmod', 'chown'
            ]
            
            if base_command in dangerous_commands:
                result["warnings"].append(f"Potentially dangerous command: {base_command}")
                
            # Check for shell injection attempts
            dangerous_chars = [';', '&', '|', '`', '$', '(', ')']
            if any(char in command for char in dangerous_chars):
                result["warnings"].append("Command contains shell metacharacters")
                
            result["command_info"] = {
                "base_command": base_command,
                "args": parts[1:] if len(parts) > 1 else [],
                "full_command": command
            }
            
            result["success"] = True
            return result
            
        except Exception as e:
            result["error"] = f"Command validation error: {e}"
            return result
