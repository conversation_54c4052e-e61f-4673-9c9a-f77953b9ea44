"""
Command Execution Tool

This module implements secure command execution with:
- Terminal command execution with output capture
- Security validation and command filtering
- Timeout handling and process management
- Real-time output streaming
"""

import asyncio
import logging
import os
import signal
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, AsyncGenerator
import shlex

logger = logging.getLogger(__name__)


class CommandExecutor:
    """
    Secure command execution with validation and output capture
    """
    
    def __init__(self, config: Dict[str, Any], ignore_controller, security_validator, workspace_path: Optional[Path] = None):
        self.config = config
        self.ignore_controller = ignore_controller
        self.security_validator = security_validator

        # Use provided workspace_path or fall back to config
        if workspace_path:
            self.workspace_path = workspace_path
        else:
            workspace_config = config.get("workspace", {})
            workspace_dir = workspace_config.get("default_directory", ".")

            # Check if temp workspace is enabled
            temp_config = workspace_config.get("temp_workspace", {})
            if temp_config.get("enabled", True):
                temp_dir_name = temp_config.get("directory_name", "temp")
                base_path = Path(workspace_dir).resolve()
                self.workspace_path = base_path / temp_dir_name
            else:
                self.workspace_path = Path(workspace_dir).resolve()

        self.default_timeout = config.get("command", {}).get("timeout", 30)
        self.max_output_size = config.get("command", {}).get("max_output_size", 1024 * 1024)  # 1MB
        
    async def execute_command(self, command: str, timeout: Optional[int] = None, 
                            working_dir: Optional[str] = None, stream_output: bool = False) -> Dict[str, Any]:
        """
        Execute a terminal command with security validation
        """
        result = {
            "success": False,
            "stdout": "",
            "stderr": "",
            "return_code": None,
            "error": None,
            "command_info": {},
            "execution_time": 0.0
        }
        
        try:
            # Security validation
            validation = self.security_validator.validate_command(command)
            if not validation["success"]:
                result["error"] = validation["error"]
                return result
                
            # Check ignore patterns for file access
            blocked_file = self.ignore_controller.validate_command(command)
            if blocked_file:
                result["error"] = f"Command blocked - accesses ignored file: {blocked_file}"
                return result
                
            # Set working directory
            if working_dir:
                work_path = Path(working_dir)
                if not work_path.is_absolute():
                    work_path = self.workspace_path / working_dir
            else:
                work_path = self.workspace_path
                
            if not work_path.exists():
                result["error"] = f"Working directory not found: {working_dir or '.'}"
                return result
                
            # Set timeout
            exec_timeout = timeout or self.default_timeout
            
            # Execute command
            start_time = asyncio.get_event_loop().time()
            
            if stream_output:
                # Streaming execution
                async for chunk in self._execute_streaming(command, str(work_path), exec_timeout):
                    if chunk["type"] == "stdout":
                        result["stdout"] += chunk["data"]
                    elif chunk["type"] == "stderr":
                        result["stderr"] += chunk["data"]
                    elif chunk["type"] == "exit":
                        result["return_code"] = chunk["code"]
                        break
                    elif chunk["type"] == "error":
                        result["error"] = chunk["message"]
                        return result
            else:
                # Non-streaming execution
                exec_result = await self._execute_simple(command, str(work_path), exec_timeout)
                result.update(exec_result)
                
            end_time = asyncio.get_event_loop().time()
            result["execution_time"] = end_time - start_time
            
            # Check output size limits
            total_output_size = len(result["stdout"]) + len(result["stderr"])
            if total_output_size > self.max_output_size:
                result["stdout"] = result["stdout"][:self.max_output_size // 2]
                result["stderr"] = result["stderr"][:self.max_output_size // 2]
                result["error"] = f"Output truncated (exceeded {self.max_output_size} bytes)"
                
            result.update({
                "success": result["return_code"] == 0 if result["return_code"] is not None else False,
                "command_info": validation["command_info"]
            })
            
            logger.info(f"Command executed: {command[:50]}... (exit code: {result['return_code']})")
            return result
            
        except Exception as e:
            result["error"] = f"Command execution error: {e}"
            logger.error(f"Command execution error: {e}")
            
        return result
        
    async def _execute_simple(self, command: str, working_dir: str, timeout: int) -> Dict[str, Any]:
        """Execute command and wait for completion"""
        try:
            # Create subprocess
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=working_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=self._get_environment()
            )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=timeout
                )
                
                return {
                    "stdout": stdout.decode('utf-8', errors='replace'),
                    "stderr": stderr.decode('utf-8', errors='replace'),
                    "return_code": process.returncode
                }
                
            except asyncio.TimeoutError:
                # Kill process on timeout
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass
                    
                return {
                    "stdout": "",
                    "stderr": f"Command timed out after {timeout} seconds",
                    "return_code": -1,
                    "error": f"Command timed out after {timeout} seconds"
                }
                
        except Exception as e:
            return {
                "stdout": "",
                "stderr": str(e),
                "return_code": -1,
                "error": f"Failed to execute command: {e}"
            }
            
    async def _execute_streaming(self, command: str, working_dir: str, timeout: int) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute command with streaming output"""
        try:
            # Create subprocess
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=working_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=self._get_environment()
            )
            
            # Stream output
            async def read_stream(stream, stream_type):
                while True:
                    try:
                        line = await stream.readline()
                        if not line:
                            break
                        yield {
                            "type": stream_type,
                            "data": line.decode('utf-8', errors='replace')
                        }
                    except Exception as e:
                        yield {
                            "type": "error",
                            "message": f"Error reading {stream_type}: {e}"
                        }
                        break
                        
            # Create tasks for stdout and stderr
            stdout_task = asyncio.create_task(
                self._collect_stream_output(read_stream(process.stdout, "stdout"))
            )
            stderr_task = asyncio.create_task(
                self._collect_stream_output(read_stream(process.stderr, "stderr"))
            )
            
            # Wait for process completion with timeout
            try:
                await asyncio.wait_for(process.wait(), timeout=timeout)
            except asyncio.TimeoutError:
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass
                yield {
                    "type": "error",
                    "message": f"Command timed out after {timeout} seconds"
                }
                return
                
            # Collect remaining output
            stdout_output = await stdout_task
            stderr_output = await stderr_task
            
            # Yield collected output
            for chunk in stdout_output:
                yield chunk
            for chunk in stderr_output:
                yield chunk
                
            # Yield exit code
            yield {
                "type": "exit",
                "code": process.returncode
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "message": f"Failed to execute command: {e}"
            }
            
    async def _collect_stream_output(self, stream_generator) -> List[Dict[str, Any]]:
        """Collect output from stream generator"""
        output = []
        async for chunk in stream_generator:
            output.append(chunk)
        return output
        
    def _get_environment(self) -> Dict[str, str]:
        """Get environment variables for command execution"""
        env = os.environ.copy()
        
        # Add workspace to PATH if needed
        if "PATH" in env:
            env["PATH"] = f"{self.workspace_path}{os.pathsep}{env['PATH']}"
        else:
            env["PATH"] = str(self.workspace_path)
            
        # Set working directory environment variable
        env["PWD"] = str(self.workspace_path)
        
        return env
        
    async def get_command_history(self) -> List[Dict[str, Any]]:
        """Get command execution history (placeholder)"""
        # In a real implementation, this would return command history
        return []
        
    async def kill_process(self, pid: int) -> Dict[str, Any]:
        """Kill a running process"""
        result = {
            "success": False,
            "error": None
        }
        
        try:
            os.kill(pid, signal.SIGTERM)
            result["success"] = True
            logger.info(f"Killed process {pid}")
            
        except ProcessLookupError:
            result["error"] = f"Process {pid} not found"
        except PermissionError:
            result["error"] = f"Permission denied to kill process {pid}"
        except Exception as e:
            result["error"] = f"Error killing process {pid}: {e}"
            
        return result
        
    def validate_shell_command(self, command: str) -> bool:
        """Validate if command is safe to execute"""
        # Basic validation - in production, this would be more comprehensive
        dangerous_patterns = [
            'rm -rf /',
            'format',
            'fdisk',
            'mkfs',
            'dd if=',
            'shutdown',
            'reboot',
            'halt'
        ]
        
        command_lower = command.lower()
        for pattern in dangerous_patterns:
            if pattern in command_lower:
                return False
                
        return True
