"""
File Operations Tools

This module implements secure file operations with:
- Read file with encoding detection
- Write file with user approval
- Replace in file using SEARCH/REPLACE blocks
- List files with filtering
- Comprehensive security validation
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import difflib

# Optional imports with fallbacks
try:
    import chardet
    HAS_CHARDET = True
except ImportError:
    HAS_CHARDET = False

logger = logging.getLogger(__name__)


class FileOperations:
    """
    Secure file operations with validation and user approval
    """
    
    def __init__(self, config: Dict[str, Any], ignore_controller, security_validator, workspace_path: Optional[Path] = None, workspace_session=None):
        self.config = config
        self.ignore_controller = ignore_controller
        self.security_validator = security_validator
        self.workspace_session = workspace_session

        # Use provided workspace_path or fall back to config
        if workspace_path:
            self.workspace_path = workspace_path
        else:
            workspace_config = config.get("workspace", {})
            workspace_dir = workspace_config.get("default_directory", ".")

            # Check if temp workspace is enabled
            temp_config = workspace_config.get("temp_workspace", {})
            if temp_config.get("enabled", True):
                temp_dir_name = temp_config.get("directory_name", "temp")
                base_path = Path(workspace_dir).resolve()
                self.workspace_path = base_path / temp_dir_name
            else:
                self.workspace_path = Path(workspace_dir).resolve()
        
    async def read_file(self, path: str) -> Dict[str, Any]:
        """
        Read file contents with encoding detection and security validation
        """
        result = {
            "success": False,
            "content": "",
            "error": None,
            "file_info": {}
        }
        
        try:
            # Security validation
            validation = self.security_validator.validate_file_read(path, str(self.workspace_path))
            if not validation["success"]:
                result["error"] = validation["error"]
                return result
                
            # Ignore pattern check
            if not self.ignore_controller.validate_access(path):
                result["error"] = f"File access denied by ignore patterns: {path}"
                return result
                
            abs_path = Path(path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / path
                
            # Read file with encoding detection
            with open(abs_path, 'rb') as f:
                raw_data = f.read()
                
            # Detect encoding
            if HAS_CHARDET:
                encoding_result = chardet.detect(raw_data)
                encoding = encoding_result.get('encoding', 'utf-8')
                confidence = encoding_result.get('confidence', 0.0)
            else:
                # Fallback encoding detection
                encoding = 'utf-8'
                confidence = 0.8
                # Try to detect if it's likely UTF-8
                try:
                    raw_data.decode('utf-8')
                except UnicodeDecodeError:
                    encoding = 'latin-1'
                    confidence = 0.5
            
            # Decode content
            try:
                content = raw_data.decode(encoding)
            except UnicodeDecodeError:
                # Fallback to utf-8 with error handling
                content = raw_data.decode('utf-8', errors='replace')
                encoding = 'utf-8'
                
            result.update({
                "success": True,
                "content": content,
                "file_info": {
                    **validation["file_info"],
                    "encoding": encoding,
                    "encoding_confidence": confidence,
                    "line_count": content.count('\n') + 1 if content else 0
                }
            })
            
            logger.info(f"Successfully read file: {path} ({len(content)} chars)")
            return result
            
        except FileNotFoundError:
            result["error"] = f"File not found: {path}"
        except PermissionError:
            result["error"] = f"Permission denied: {path}"
        except Exception as e:
            result["error"] = f"Error reading file {path}: {e}"
            logger.error(f"Error reading file {path}: {e}")
            
        return result
        
    async def write_file(self, path: str, content: str, require_approval: bool = True) -> Dict[str, Any]:
        """
        Write content to file with security validation and user approval
        """
        result = {
            "success": False,
            "error": None,
            "operation_info": {},
            "diff": None
        }
        
        try:
            # Security validation
            validation = self.security_validator.validate_file_write(path, content, str(self.workspace_path))
            if not validation["success"]:
                result["error"] = validation["error"]
                return result
                
            # Ignore pattern check
            if not self.ignore_controller.validate_access(path):
                result["error"] = f"File access denied by ignore patterns: {path}"
                return result
                
            abs_path = Path(path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / path
                
            # Check if file exists and read original content
            original_content = ""
            file_exists = abs_path.exists()
            
            if file_exists:
                try:
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        original_content = f.read()
                except UnicodeDecodeError:
                    with open(abs_path, 'r', encoding='utf-8', errors='replace') as f:
                        original_content = f.read()
                        
            # Generate diff for user review
            diff_lines = list(difflib.unified_diff(
                original_content.splitlines(keepends=True),
                content.splitlines(keepends=True),
                fromfile=f"{path} (original)",
                tofile=f"{path} (new)",
                lineterm=""
            ))
            diff_text = ''.join(diff_lines)
            
            # User approval (if required)
            if require_approval and diff_text:
                approval_result = await self._request_approval(
                    operation="write_file",
                    path=path,
                    diff=diff_text,
                    operation_info=validation["operation_info"]
                )
                if not approval_result["approved"]:
                    result["error"] = "Operation cancelled by user"
                    return result
                    
            # Create parent directories if needed
            abs_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Track file in workspace session
            if self.workspace_session:
                if file_exists:
                    self.workspace_session.track_file_modified(str(abs_path))
                else:
                    self.workspace_session.track_file_created(str(abs_path))

            result.update({
                "success": True,
                "operation_info": validation["operation_info"],
                "diff": diff_text if diff_text else None
            })

            logger.info(f"Successfully wrote file: {path} ({len(content)} chars)")
            return result
            
        except PermissionError:
            result["error"] = f"Permission denied: {path}"
        except Exception as e:
            result["error"] = f"Error writing file {path}: {e}"
            logger.error(f"Error writing file {path}: {e}")
            
        return result

    async def create_directory(self, path: str, require_approval: bool = True) -> Dict[str, Any]:
        """
        Create a directory with security validation and user approval
        """
        result = {
            "success": False,
            "error": None,
            "operation_info": {},
            "created_path": None
        }

        try:
            # Security validation for directory creation
            validation = self.security_validator.validate_file_write(path, "", str(self.workspace_path))
            if not validation["success"]:
                result["error"] = validation["error"]
                return result

            # Ignore pattern check
            if not self.ignore_controller.validate_access(path):
                result["error"] = f"Directory access denied by ignore patterns: {path}"
                return result

            abs_path = Path(path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / path

            # Check if directory already exists
            if abs_path.exists():
                if abs_path.is_dir():
                    result.update({
                        "success": True,
                        "operation_info": {"message": "Directory already exists"},
                        "created_path": str(abs_path)
                    })
                    return result
                else:
                    result["error"] = f"Path exists but is not a directory: {path}"
                    return result

            # User approval (if required)
            if require_approval:
                approval_result = await self._request_approval(
                    operation="create_directory",
                    path=path,
                    operation_info={"action": "create directory", "path": str(abs_path)}
                )
                if not approval_result["approved"]:
                    result["error"] = "Operation cancelled by user"
                    return result

            # Create directory
            abs_path.mkdir(parents=True, exist_ok=True)

            # Track directory creation in workspace session
            if self.workspace_session:
                self.workspace_session.track_file_created(str(abs_path))

            result.update({
                "success": True,
                "operation_info": validation["operation_info"],
                "created_path": str(abs_path)
            })

            logger.info(f"Successfully created directory: {path}")
            return result

        except PermissionError:
            result["error"] = f"Permission denied: {path}"
        except Exception as e:
            result["error"] = f"Error creating directory {path}: {e}"
            logger.error(f"Error creating directory {path}: {e}")

        return result

    async def replace_in_file(self, path: str, diff: str, require_approval: bool = True) -> Dict[str, Any]:
        """
        Replace content in file using SEARCH/REPLACE blocks
        """
        result = {
            "success": False,
            "error": None,
            "replacements": [],
            "final_content": None
        }
        
        try:
            # Read current file content
            read_result = await self.read_file(path)
            if not read_result["success"]:
                result["error"] = read_result["error"]
                return result
                
            original_content = read_result["content"]
            modified_content = original_content
            
            # Parse SEARCH/REPLACE blocks
            replacements = self._parse_search_replace_blocks(diff)
            if not replacements:
                result["error"] = "No valid SEARCH/REPLACE blocks found"
                return result
                
            # Apply replacements
            for i, (search_text, replace_text) in enumerate(replacements):
                if search_text not in modified_content:
                    result["error"] = f"Search text not found in file (block {i+1}): {search_text[:100]}..."
                    return result
                    
                # Count occurrences
                count = modified_content.count(search_text)
                if count > 1:
                    logger.warning(f"Search text appears {count} times, replacing all occurrences")
                    
                modified_content = modified_content.replace(search_text, replace_text)
                result["replacements"].append({
                    "search": search_text,
                    "replace": replace_text,
                    "occurrences": count
                })
                
            # Generate diff for review
            diff_lines = list(difflib.unified_diff(
                original_content.splitlines(keepends=True),
                modified_content.splitlines(keepends=True),
                fromfile=f"{path} (original)",
                tofile=f"{path} (modified)",
                lineterm=""
            ))
            diff_text = ''.join(diff_lines)
            
            # User approval (if required)
            if require_approval:
                approval_result = await self._request_approval(
                    operation="replace_in_file",
                    path=path,
                    diff=diff_text,
                    replacements=result["replacements"]
                )
                if not approval_result["approved"]:
                    result["error"] = "Operation cancelled by user"
                    return result
                    
            # Write modified content
            write_result = await self.write_file(path, modified_content, require_approval=False)
            if not write_result["success"]:
                result["error"] = write_result["error"]
                return result
                
            result.update({
                "success": True,
                "final_content": modified_content,
                "diff": diff_text
            })
            
            logger.info(f"Successfully replaced content in file: {path} ({len(result['replacements'])} replacements)")
            return result
            
        except Exception as e:
            result["error"] = f"Error replacing content in {path}: {e}"
            logger.error(f"Error replacing content in {path}: {e}")
            
        return result
        
    def _parse_search_replace_blocks(self, diff: str) -> List[Tuple[str, str]]:
        """
        Parse SEARCH/REPLACE blocks from diff text
        Format:
        ------- SEARCH
        [content to find]
        =======
        [content to replace with]
        +++++++ REPLACE
        """
        replacements = []
        
        # Split by SEARCH blocks
        blocks = re.split(r'^------- SEARCH\s*$', diff, flags=re.MULTILINE)
        
        for block in blocks[1:]:  # Skip first empty block
            # Find the separator and end marker
            if '=======' not in block or '+++++++ REPLACE' not in block:
                continue
                
            parts = block.split('=======', 1)
            if len(parts) != 2:
                continue
                
            search_part = parts[0].strip()
            replace_part = parts[1].split('+++++++ REPLACE', 1)[0].strip()
            
            if search_part:  # Allow empty replace_part for deletion
                replacements.append((search_part, replace_part))
                
        return replacements
        
    async def _request_approval(self, operation: str, path: str, **kwargs) -> Dict[str, Any]:
        """
        Request user approval for file operation
        This integrates with the CLI interface for interactive approval
        """
        # Import here to avoid circular imports
        from ..ui.cli import CLIInterface

        # Get CLI interface from config or create a temporary one
        cli = CLIInterface(self.config)

        # Request approval through CLI
        return cli.request_approval(operation, path, **kwargs)

    async def list_files(self, path: str = ".", recursive: bool = False, limit: int = 1000) -> Dict[str, Any]:
        """
        List files and directories in a given path
        """
        result = {
            "success": False,
            "files": [],
            "directories": [],
            "error": None,
            "total_count": 0,
            "truncated": False
        }

        try:
            abs_path = Path(path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / path

            if not abs_path.exists():
                result["error"] = f"Directory not found: {path}"
                return result

            if not abs_path.is_dir():
                result["error"] = f"Path is not a directory: {path}"
                return result

            # Check access permissions
            if not self.ignore_controller.validate_access(str(abs_path)):
                result["error"] = f"Directory access denied by ignore patterns: {path}"
                return result

            files = []
            directories = []
            count = 0

            if recursive:
                # Recursive listing
                for item in abs_path.rglob("*"):
                    if count >= limit:
                        result["truncated"] = True
                        break

                    # Check ignore patterns
                    rel_path = item.relative_to(self.workspace_path)
                    if not self.ignore_controller.validate_access(str(rel_path)):
                        continue

                    item_info = self._get_item_info(item, abs_path)

                    if item.is_file():
                        files.append(item_info)
                    elif item.is_dir():
                        directories.append(item_info)

                    count += 1
            else:
                # Non-recursive listing
                try:
                    for item in abs_path.iterdir():
                        if count >= limit:
                            result["truncated"] = True
                            break

                        # Check ignore patterns
                        rel_path = item.relative_to(self.workspace_path)
                        if not self.ignore_controller.validate_access(str(rel_path)):
                            continue

                        item_info = self._get_item_info(item, abs_path)

                        if item.is_file():
                            files.append(item_info)
                        elif item.is_dir():
                            directories.append(item_info)

                        count += 1

                except PermissionError:
                    result["error"] = f"Permission denied accessing directory: {path}"
                    return result

            # Sort results
            files.sort(key=lambda x: x["name"].lower())
            directories.sort(key=lambda x: x["name"].lower())

            result.update({
                "success": True,
                "files": files,
                "directories": directories,
                "total_count": len(files) + len(directories)
            })

            logger.info(f"Listed directory: {path} ({len(files)} files, {len(directories)} dirs)")
            return result

        except Exception as e:
            result["error"] = f"Error listing directory {path}: {e}"
            logger.error(f"Error listing directory {path}: {e}")

        return result

    async def get_workspace_info(self) -> Dict[str, Any]:
        """
        Get comprehensive information about the current workspace
        """
        result = {
            "success": False,
            "workspace_path": str(self.workspace_path),
            "files": [],
            "directories": [],
            "total_files": 0,
            "total_directories": 0,
            "error": None
        }

        try:
            if not self.workspace_path.exists():
                result["error"] = f"Workspace directory does not exist: {self.workspace_path}"
                return result

            # Get recursive listing of all files and directories
            list_result = await self.list_files(".", recursive=True, limit=10000)
            if not list_result["success"]:
                result["error"] = list_result["error"]
                return result

            result.update({
                "success": True,
                "files": list_result["files"],
                "directories": list_result["directories"],
                "total_files": len(list_result["files"]),
                "total_directories": len(list_result["directories"])
            })

            logger.info(f"Workspace info: {result['total_files']} files, {result['total_directories']} directories")
            return result

        except Exception as e:
            result["error"] = f"Error getting workspace info: {e}"
            logger.error(f"Error getting workspace info: {e}")

        return result

    async def get_file_relationships(self, path: str) -> Dict[str, Any]:
        """
        Analyze file relationships and dependencies within the workspace
        """
        result = {
            "success": False,
            "file_path": path,
            "related_files": [],
            "imports": [],
            "references": [],
            "error": None
        }

        try:
            # Read the file content
            read_result = await self.read_file(path)
            if not read_result["success"]:
                result["error"] = read_result["error"]
                return result

            content = read_result["content"]
            file_ext = Path(path).suffix.lower()

            # Analyze based on file type
            if file_ext == ".py":
                result["imports"] = self._extract_python_imports(content)
            elif file_ext in [".js", ".ts"]:
                result["imports"] = self._extract_js_imports(content)
            elif file_ext in [".json", ".yaml", ".yml"]:
                result["references"] = self._extract_config_references(content)

            # Find related files in workspace
            workspace_info = await self.get_workspace_info()
            if workspace_info["success"]:
                result["related_files"] = self._find_related_files(path, workspace_info["files"])

            result["success"] = True
            return result

        except Exception as e:
            result["error"] = f"Error analyzing file relationships: {e}"
            logger.error(f"Error analyzing file relationships for {path}: {e}")

        return result

    def _extract_python_imports(self, content: str) -> List[str]:
        """Extract Python import statements"""
        imports = []
        import_patterns = [
            r'^import\s+([^\s#]+)',
            r'^from\s+([^\s#]+)\s+import',
        ]

        for line in content.split('\n'):
            line = line.strip()
            for pattern in import_patterns:
                match = re.match(pattern, line)
                if match:
                    imports.append(match.group(1))

        return list(set(imports))

    def _extract_js_imports(self, content: str) -> List[str]:
        """Extract JavaScript/TypeScript import statements"""
        imports = []
        import_patterns = [
            r'import.*from\s+[\'"]([^\'"]+)[\'"]',
            r'require\([\'"]([^\'"]+)[\'"]\)',
        ]

        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            imports.extend(matches)

        return list(set(imports))

    def _extract_config_references(self, content: str) -> List[str]:
        """Extract file references from configuration files"""
        references = []
        # Look for file paths in quotes
        file_patterns = [
            r'[\'"]([^\'"\s]+\.[a-zA-Z0-9]+)[\'"]',  # Files with extensions
            r'[\'"](\./[^\'"\s]+)[\'"]',  # Relative paths
        ]

        for pattern in file_patterns:
            matches = re.findall(pattern, content)
            references.extend(matches)

        return list(set(references))

    def _find_related_files(self, target_path: str, all_files: List[Dict[str, Any]]) -> List[str]:
        """Find files that might be related to the target file"""
        related = []
        target_name = Path(target_path).stem
        target_dir = str(Path(target_path).parent)

        for file_info in all_files:
            file_path = file_info["path"]
            file_name = Path(file_path).stem
            file_dir = str(Path(file_path).parent)

            # Skip the target file itself
            if file_path == target_path:
                continue

            # Files in the same directory
            if file_dir == target_dir:
                related.append(file_path)
            # Files with similar names
            elif target_name in file_name or file_name in target_name:
                related.append(file_path)

        return related

    async def get_session_info(self) -> Dict[str, Any]:
        """Get current workspace session information"""
        result = {
            "success": False,
            "session_info": {},
            "error": None
        }

        try:
            if not self.workspace_session:
                result["error"] = "No active workspace session"
                return result

            session_summary = self.workspace_session.get_session_summary()
            tracked_files = self.workspace_session.get_all_tracked_files()

            result.update({
                "success": True,
                "session_info": {
                    **session_summary,
                    "tracked_files_list": tracked_files
                }
            })

            return result

        except Exception as e:
            result["error"] = f"Error getting session info: {e}"
            logger.error(f"Error getting session info: {e}")

        return result

    def _get_item_info(self, item: Path, base_path: Path) -> Dict[str, Any]:
        """Get information about a file or directory item"""
        try:
            stat = item.stat()
            rel_path = item.relative_to(base_path)

            info = {
                "name": item.name,
                "path": str(rel_path),
                "absolute_path": str(item),
                "type": "file" if item.is_file() else "directory",
                "size": stat.st_size if item.is_file() else None,
                "modified": stat.st_mtime,
                "permissions": oct(stat.st_mode)[-3:],
            }

            if item.is_file():
                info["extension"] = item.suffix

            return info

        except Exception as e:
            logger.warning(f"Error getting info for {item}: {e}")
            return {
                "name": item.name,
                "path": str(item.relative_to(base_path)),
                "type": "unknown",
                "error": str(e)
            }
