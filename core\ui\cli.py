"""
Command Line Interface

This module implements the CLI for PyIDE with:
- Interactive chat interface
- Real-time AI response streaming
- Tool execution with user approval
- Rich text formatting and syntax highlighting
"""

import asyncio
import logging
import sys
from typing import Dict, List, Optional, Any
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.table import Table
import click

logger = logging.getLogger(__name__)


class CLIInterface:
    """
    Command line interface for PyIDE
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.console = Console()
        self.ui_config = config.get("ui", {})
        self.colors_enabled = self.ui_config.get("colors", True)
        self.show_diffs = self.ui_config.get("show_diffs", True)
        
    def display_welcome(self):
        """Display welcome message"""
        welcome_text = """
# Welcome to PyIDE

An AI-powered Python IDE with secure file operations and real-time interaction.

**Available Commands:**
- Type your request in natural language
- Use `/help` for command reference
- Use `/exit` to quit
- Use `/clear` to clear conversation history
- Use `/status` to show system status

**Features:**
- Real-time AI streaming responses
- Secure file operations with user approval
- Comprehensive tool support
- Diff-based editing with preview
        """
        
        self.console.print(Panel(
            Markdown(welcome_text),
            title="PyIDE - AI-Powered Development Environment",
            border_style="blue"
        ))
        
    def get_user_input(self) -> str:
        """Get user input with prompt"""
        try:
            return Prompt.ask(
                "[bold blue]PyIDE[/bold blue]",
                default="",
                show_default=False
            )
        except (KeyboardInterrupt, EOFError):
            return "/exit"
            
    def display_ai_response_start(self):
        """Display start of AI response"""
        self.console.print("\n[bold green]Assistant:[/bold green]")
        
    def display_ai_text(self, text: str):
        """Display AI text response"""
        self.console.print(text, end="")
        
    def display_tool_call(self, tool_name: str, params: Dict[str, str]):
        """Display tool call information"""
        self.console.print(f"\n\n[bold yellow]🔧 Tool Call: {tool_name}[/bold yellow]")
        
        # Create parameter table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan")
        table.add_column("Value", style="white")
        
        for param, value in params.items():
            # Truncate long values
            display_value = value[:100] + "..." if len(value) > 100 else value
            table.add_row(param, display_value)
            
        self.console.print(table)
        
    def display_tool_result(self, tool_name: str, result: Dict[str, Any], success: bool = True):
        """Display tool execution result"""
        status_color = "green" if success else "red"
        status_text = "SUCCESS" if success else "ERROR"
        
        self.console.print(f"\n[bold {status_color}]📋 Tool Result: {tool_name} - {status_text}[/bold {status_color}]")
        
        if "error" in result and result["error"]:
            self.console.print(f"[red]Error: {result['error']}[/red]")
        elif "content" in result:
            # File content result
            content = result["content"]
            if len(content) > 500:
                self.console.print(f"[dim]Content preview (first 500 chars):[/dim]")
                self.console.print(content[:500] + "...")
            else:
                self.console.print(content)
        elif "files" in result:
            # File listing result
            files = result["files"]
            dirs = result.get("directories", [])
            
            self.console.print(f"[dim]Found {len(files)} files and {len(dirs)} directories[/dim]")
            
            if dirs:
                self.console.print("\n[bold]Directories:[/bold]")
                for dir_info in dirs[:10]:  # Show first 10
                    self.console.print(f"  📁 {dir_info['name']}")
                    
            if files:
                self.console.print("\n[bold]Files:[/bold]")
                for file_info in files[:20]:  # Show first 20
                    size = file_info.get('size', 0)
                    size_str = self._format_file_size(size) if size else ""
                    self.console.print(f"  📄 {file_info['name']} {size_str}")
                    
        elif "matches" in result:
            # Search result
            matches = result["matches"]
            total_matches = result.get("search_info", {}).get("total_matches", 0)
            
            self.console.print(f"[dim]Found {total_matches} matches in {len(matches)} files[/dim]")
            
            for file_match in matches[:5]:  # Show first 5 files
                self.console.print(f"\n[bold]📄 {file_match['file']}[/bold] ({file_match['match_count']} matches)")
                
                for match in file_match['matches'][:3]:  # Show first 3 matches per file
                    line_num = match['line_number']
                    line_content = match['line_content']
                    self.console.print(f"  [cyan]Line {line_num}:[/cyan] {line_content[:100]}")
                    
    def request_approval(self, operation: str, path: str, **kwargs) -> Dict[str, Any]:
        """Request user approval for file operation"""
        self.console.print(f"\n[bold yellow]🔐 Approval Required[/bold yellow]")
        self.console.print(f"Operation: [bold]{operation}[/bold]")
        self.console.print(f"File: [cyan]{path}[/cyan]")
        
        # Show diff if available
        if "diff" in kwargs and kwargs["diff"] and self.show_diffs:
            self.display_diff(kwargs["diff"])
            
        # Show operation info
        if "operation_info" in kwargs:
            info = kwargs["operation_info"]
            if info.get("file_exists"):
                self.console.print("[yellow]⚠️  File exists and will be overwritten[/yellow]")
            else:
                self.console.print("[green]✨ New file will be created[/green]")
                
        # Get user decision
        try:
            approved = Confirm.ask("\nApprove this operation?", default=True)
            feedback = None
            
            if not approved:
                feedback = Prompt.ask(
                    "Optional feedback for the AI",
                    default="",
                    show_default=False
                )
                
            return {
                "approved": approved,
                "feedback": feedback
            }
            
        except (KeyboardInterrupt, EOFError):
            return {
                "approved": False,
                "feedback": "Operation cancelled by user"
            }
            
    def display_diff(self, diff_text: str):
        """Display diff with syntax highlighting"""
        if not diff_text.strip():
            return
            
        self.console.print("\n[bold]📋 Changes Preview:[/bold]")
        
        try:
            # Use syntax highlighting for diff
            syntax = Syntax(diff_text, "diff", theme="monokai", line_numbers=True)
            self.console.print(Panel(syntax, title="Diff", border_style="yellow"))
        except Exception:
            # Fallback to plain text
            self.console.print(Panel(diff_text, title="Diff", border_style="yellow"))
            
    def display_error(self, error: str):
        """Display error message"""
        self.console.print(f"\n[bold red]❌ Error:[/bold red] {error}")
        
    def display_warning(self, warning: str):
        """Display warning message"""
        self.console.print(f"[bold yellow]⚠️  Warning:[/bold yellow] {warning}")
        
    def display_info(self, info: str):
        """Display info message"""
        self.console.print(f"[bold blue]ℹ️  Info:[/bold blue] {info}")
        
    def display_success(self, message: str):
        """Display success message"""
        self.console.print(f"[bold green]✅ Success:[/bold green] {message}")
        
    def show_progress(self, description: str):
        """Show progress spinner"""
        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        )
        
    def display_status(self, status_info: Dict[str, Any]):
        """Display system status"""
        table = Table(title="PyIDE Status", show_header=True, header_style="bold magenta")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Details", style="dim")
        
        for component, info in status_info.items():
            status = "✅ OK" if info.get("status") == "ok" else "❌ ERROR"
            details = info.get("details", "")
            table.add_row(component, status, details)
            
        self.console.print(table)
        
    def display_help(self):
        """Display help information"""
        help_text = """
# PyIDE Help

## Natural Language Commands
Just type what you want to do in natural language:
- "Create a Python script that prints hello world"
- "Read the contents of main.py"
- "Search for TODO comments in all Python files"
- "List all files in the src directory"

## Special Commands
- `/help` - Show this help
- `/exit` - Exit PyIDE
- `/clear` - Clear conversation history
- `/status` - Show system status
- `/config` - Show configuration

## Tool Reference
- **read_file**: Read file contents
- **write_to_file**: Create or overwrite files
- **replace_in_file**: Make targeted edits using SEARCH/REPLACE
- **list_files**: List directory contents
- **search_files**: Search files with regex
- **execute_command**: Run terminal commands

## Security Features
- All file operations require user approval
- `.pyideignore` file support for access control
- Workspace boundary enforcement
- Command validation and filtering
        """
        
        self.console.print(Panel(
            Markdown(help_text),
            title="PyIDE Help",
            border_style="blue"
        ))
        
    def _format_file_size(self, size: int) -> str:
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"({size:.1f} {unit})"
            size /= 1024.0
        return f"({size:.1f} TB)"
        
    def clear_screen(self):
        """Clear the terminal screen"""
        self.console.clear()
        
    def print_separator(self):
        """Print a visual separator"""
        self.console.print("\n" + "─" * 80 + "\n")
