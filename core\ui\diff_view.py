"""
Diff Viewer for File Changes

This module implements diff viewing functionality with:
- Side-by-side diff display
- Syntax highlighting
- Interactive approval workflow
- Change statistics and analysis
"""

import difflib
import logging
from typing import Dict, List, Optional, Any, Tuple
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.syntax import Syntax
from pathlib import Path

logger = logging.getLogger(__name__)


class DiffViewer:
    """
    Advanced diff viewer with syntax highlighting and interactive features
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.console = Console()
        self.ui_config = config.get("ui", {})
        self.context_lines = self.ui_config.get("diff_context_lines", 3)
        self.max_display_lines = self.ui_config.get("max_display_lines", 50)
        
    def display_unified_diff(self, original: str, modified: str, filename: str = "file") -> str:
        """
        Generate and display unified diff
        """
        original_lines = original.splitlines(keepends=True)
        modified_lines = modified.splitlines(keepends=True)
        
        diff_lines = list(difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile=f"{filename} (original)",
            tofile=f"{filename} (modified)",
            lineterm=""
        ))
        
        diff_text = ''.join(diff_lines)
        
        if diff_text:
            self._display_diff_with_highlighting(diff_text, filename)
            
        return diff_text
        
    def display_side_by_side_diff(self, original: str, modified: str, filename: str = "file"):
        """
        Display side-by-side diff comparison
        """
        original_lines = original.splitlines()
        modified_lines = modified.splitlines()
        
        # Generate diff operations
        diff_ops = list(difflib.ndiff(original_lines, modified_lines))
        
        # Create side-by-side display
        left_lines = []
        right_lines = []
        line_numbers_left = []
        line_numbers_right = []
        
        left_line_num = 1
        right_line_num = 1
        
        for line in diff_ops:
            if line.startswith('  '):  # Unchanged line
                content = line[2:]
                left_lines.append(("unchanged", content))
                right_lines.append(("unchanged", content))
                line_numbers_left.append(left_line_num)
                line_numbers_right.append(right_line_num)
                left_line_num += 1
                right_line_num += 1
            elif line.startswith('- '):  # Deleted line
                content = line[2:]
                left_lines.append(("deleted", content))
                right_lines.append(("empty", ""))
                line_numbers_left.append(left_line_num)
                line_numbers_right.append("")
                left_line_num += 1
            elif line.startswith('+ '):  # Added line
                content = line[2:]
                left_lines.append(("empty", ""))
                right_lines.append(("added", content))
                line_numbers_left.append("")
                line_numbers_right.append(right_line_num)
                right_line_num += 1
                
        self._display_side_by_side_table(
            left_lines, right_lines, 
            line_numbers_left, line_numbers_right,
            filename
        )
        
    def _display_diff_with_highlighting(self, diff_text: str, filename: str):
        """Display diff with syntax highlighting"""
        try:
            # Detect file type for syntax highlighting
            file_ext = Path(filename).suffix.lower()
            lexer_map = {
                '.py': 'python',
                '.js': 'javascript',
                '.ts': 'typescript',
                '.html': 'html',
                '.css': 'css',
                '.json': 'json',
                '.xml': 'xml',
                '.yaml': 'yaml',
                '.yml': 'yaml',
                '.md': 'markdown',
                '.sh': 'bash',
                '.sql': 'sql'
            }
            
            # Use diff syntax highlighting
            syntax = Syntax(
                diff_text, 
                "diff", 
                theme="monokai", 
                line_numbers=True,
                word_wrap=True
            )
            
            self.console.print(Panel(
                syntax,
                title=f"📋 Diff: {filename}",
                border_style="yellow"
            ))
            
        except Exception as e:
            logger.warning(f"Error displaying diff with highlighting: {e}")
            # Fallback to plain text
            self.console.print(Panel(
                diff_text,
                title=f"📋 Diff: {filename}",
                border_style="yellow"
            ))
            
    def _display_side_by_side_table(self, left_lines: List[Tuple[str, str]], 
                                   right_lines: List[Tuple[str, str]],
                                   line_numbers_left: List[Any], 
                                   line_numbers_right: List[Any],
                                   filename: str):
        """Display side-by-side diff in table format"""
        table = Table(
            title=f"📋 Side-by-Side Diff: {filename}",
            show_header=True,
            header_style="bold magenta",
            border_style="yellow"
        )
        
        table.add_column("Line", style="dim", width=4)
        table.add_column("Original", style="white", width=40)
        table.add_column("Line", style="dim", width=4)
        table.add_column("Modified", style="white", width=40)
        
        max_lines = min(len(left_lines), self.max_display_lines)
        
        for i in range(max_lines):
            left_type, left_content = left_lines[i] if i < len(left_lines) else ("empty", "")
            right_type, right_content = right_lines[i] if i < len(right_lines) else ("empty", "")
            
            left_line_num = str(line_numbers_left[i]) if i < len(line_numbers_left) and line_numbers_left[i] else ""
            right_line_num = str(line_numbers_right[i]) if i < len(line_numbers_right) and line_numbers_right[i] else ""
            
            # Style content based on change type
            left_styled = self._style_diff_line(left_content, left_type)
            right_styled = self._style_diff_line(right_content, right_type)
            
            table.add_row(left_line_num, left_styled, right_line_num, right_styled)
            
        if len(left_lines) > max_lines:
            table.add_row("...", "[dim]... (truncated)[/dim]", "...", "[dim]... (truncated)[/dim]")
            
        self.console.print(table)
        
    def _style_diff_line(self, content: str, line_type: str) -> str:
        """Apply styling to diff line based on type"""
        if line_type == "deleted":
            return f"[red on dark_red]{content}[/red on dark_red]"
        elif line_type == "added":
            return f"[green on dark_green]{content}[/green on dark_green]"
        elif line_type == "unchanged":
            return content
        else:  # empty
            return ""
            
    def analyze_changes(self, original: str, modified: str) -> Dict[str, Any]:
        """
        Analyze changes between original and modified content
        """
        original_lines = original.splitlines()
        modified_lines = modified.splitlines()
        
        # Calculate basic statistics
        stats = {
            "original_lines": len(original_lines),
            "modified_lines": len(modified_lines),
            "lines_added": 0,
            "lines_deleted": 0,
            "lines_modified": 0,
            "lines_unchanged": 0
        }
        
        # Use difflib to get detailed changes
        diff_ops = list(difflib.ndiff(original_lines, modified_lines))
        
        for line in diff_ops:
            if line.startswith('  '):  # Unchanged
                stats["lines_unchanged"] += 1
            elif line.startswith('- '):  # Deleted
                stats["lines_deleted"] += 1
            elif line.startswith('+ '):  # Added
                stats["lines_added"] += 1
            elif line.startswith('? '):  # Modified (indicator line)
                stats["lines_modified"] += 1
                
        # Calculate percentages
        total_original = stats["original_lines"]
        if total_original > 0:
            stats["percent_changed"] = (
                (stats["lines_added"] + stats["lines_deleted"]) / total_original * 100
            )
        else:
            stats["percent_changed"] = 100.0 if stats["modified_lines"] > 0 else 0.0
            
        return stats
        
    def display_change_summary(self, stats: Dict[str, Any]):
        """Display summary of changes"""
        table = Table(
            title="📊 Change Summary",
            show_header=True,
            header_style="bold cyan",
            border_style="blue"
        )
        
        table.add_column("Metric", style="white")
        table.add_column("Count", style="yellow")
        table.add_column("Details", style="dim")
        
        table.add_row(
            "Original Lines", 
            str(stats["original_lines"]),
            "Lines in original file"
        )
        table.add_row(
            "Modified Lines", 
            str(stats["modified_lines"]),
            "Lines in modified file"
        )
        table.add_row(
            "Lines Added", 
            f"[green]+{stats['lines_added']}[/green]",
            "New lines added"
        )
        table.add_row(
            "Lines Deleted", 
            f"[red]-{stats['lines_deleted']}[/red]",
            "Lines removed"
        )
        table.add_row(
            "Lines Unchanged", 
            str(stats["lines_unchanged"]),
            "Lines that remained the same"
        )
        table.add_row(
            "Change Percentage", 
            f"{stats['percent_changed']:.1f}%",
            "Percentage of file changed"
        )
        
        self.console.print(table)
        
    def display_file_preview(self, content: str, filename: str, max_lines: int = 20):
        """Display preview of file content"""
        lines = content.splitlines()
        
        if len(lines) <= max_lines:
            preview_content = content
            truncated = False
        else:
            preview_content = '\n'.join(lines[:max_lines])
            truncated = True
            
        try:
            # Detect file type for syntax highlighting
            file_ext = Path(filename).suffix.lower()
            lexer_map = {
                '.py': 'python',
                '.js': 'javascript',
                '.ts': 'typescript',
                '.html': 'html',
                '.css': 'css',
                '.json': 'json',
                '.xml': 'xml',
                '.yaml': 'yaml',
                '.yml': 'yaml',
                '.md': 'markdown',
                '.sh': 'bash',
                '.sql': 'sql'
            }
            
            lexer = lexer_map.get(file_ext, 'text')
            
            syntax = Syntax(
                preview_content,
                lexer,
                theme="monokai",
                line_numbers=True,
                word_wrap=True
            )
            
            title = f"📄 File Preview: {filename}"
            if truncated:
                title += f" (showing first {max_lines} lines)"
                
            self.console.print(Panel(
                syntax,
                title=title,
                border_style="blue"
            ))
            
            if truncated:
                self.console.print(f"[dim]... and {len(lines) - max_lines} more lines[/dim]")
                
        except Exception as e:
            logger.warning(f"Error displaying file preview: {e}")
            # Fallback to plain text
            self.console.print(Panel(
                preview_content,
                title=f"📄 File Preview: {filename}",
                border_style="blue"
            ))
