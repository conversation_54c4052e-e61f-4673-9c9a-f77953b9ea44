"""
Workspace Session Management

This module provides session management for the temporary workspace:
- Track files created and modified during a session
- Maintain workspace context and file relationships
- Provide session state information
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
import hashlib

logger = logging.getLogger(__name__)


class WorkspaceSession:
    """
    Manages workspace session state and file tracking
    """
    
    def __init__(self, workspace_path: Path, session_id: Optional[str] = None):
        self.workspace_path = workspace_path
        self.session_id = session_id or self._generate_session_id()
        self.start_time = datetime.now()
        
        # File tracking
        self.created_files: Set[str] = set()
        self.modified_files: Set[str] = set()
        self.deleted_files: Set[str] = set()
        self.file_checksums: Dict[str, str] = {}
        
        # Session metadata
        self.session_data: Dict[str, Any] = {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "workspace_path": str(self.workspace_path),
            "created_files": [],
            "modified_files": [],
            "deleted_files": [],
            "file_relationships": {},
            "session_notes": []
        }
        
        # Load existing session if available
        self._load_session()
        
    def _generate_session_id(self) -> str:
        """Generate a unique session ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()[:8]
        return f"session_{timestamp}_{random_suffix}"
        
    def _get_session_file_path(self) -> Path:
        """Get the path to the session metadata file"""
        return self.workspace_path / ".pyide_session.json"
        
    def _load_session(self):
        """Load existing session data if available"""
        session_file = self._get_session_file_path()
        if session_file.exists():
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                    
                # Update session data with saved information
                self.created_files.update(saved_data.get("created_files", []))
                self.modified_files.update(saved_data.get("modified_files", []))
                self.deleted_files.update(saved_data.get("deleted_files", []))
                
                # Merge session data
                self.session_data.update(saved_data)
                self.session_data["last_loaded"] = datetime.now().isoformat()
                
                logger.info(f"Loaded existing session: {self.session_id}")
                
            except Exception as e:
                logger.warning(f"Failed to load session data: {e}")
                
    def _save_session(self):
        """Save current session data to file"""
        try:
            # Update session data with current state
            self.session_data.update({
                "created_files": list(self.created_files),
                "modified_files": list(self.modified_files),
                "deleted_files": list(self.deleted_files),
                "last_saved": datetime.now().isoformat()
            })
            
            session_file = self._get_session_file_path()
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(self.session_data, f, indent=2)
                
            logger.debug(f"Saved session data: {session_file}")
            
        except Exception as e:
            logger.error(f"Failed to save session data: {e}")
            
    def track_file_created(self, file_path: str):
        """Track a file that was created in this session"""
        rel_path = self._get_relative_path(file_path)
        if rel_path:
            self.created_files.add(rel_path)
            self._update_file_checksum(rel_path)
            self._save_session()
            logger.info(f"Tracked file creation: {rel_path}")
            
    def track_file_modified(self, file_path: str):
        """Track a file that was modified in this session"""
        rel_path = self._get_relative_path(file_path)
        if rel_path:
            if rel_path not in self.created_files:
                self.modified_files.add(rel_path)
            self._update_file_checksum(rel_path)
            self._save_session()
            logger.info(f"Tracked file modification: {rel_path}")
            
    def track_file_deleted(self, file_path: str):
        """Track a file that was deleted in this session"""
        rel_path = self._get_relative_path(file_path)
        if rel_path:
            self.deleted_files.add(rel_path)
            # Remove from created/modified if it was tracked there
            self.created_files.discard(rel_path)
            self.modified_files.discard(rel_path)
            self.file_checksums.pop(rel_path, None)
            self._save_session()
            logger.info(f"Tracked file deletion: {rel_path}")
            
    def _get_relative_path(self, file_path: str) -> Optional[str]:
        """Get relative path from workspace root"""
        try:
            abs_path = Path(file_path)
            if not abs_path.is_absolute():
                abs_path = self.workspace_path / file_path
            abs_path = abs_path.resolve()
            
            rel_path = abs_path.relative_to(self.workspace_path)
            return str(rel_path)
        except ValueError:
            # Path is outside workspace
            return None
        except Exception as e:
            logger.warning(f"Error getting relative path for {file_path}: {e}")
            return None
            
    def _update_file_checksum(self, rel_path: str):
        """Update checksum for a file"""
        try:
            abs_path = self.workspace_path / rel_path
            if abs_path.exists() and abs_path.is_file():
                with open(abs_path, 'rb') as f:
                    content = f.read()
                    checksum = hashlib.md5(content).hexdigest()
                    self.file_checksums[rel_path] = checksum
        except Exception as e:
            logger.warning(f"Failed to update checksum for {rel_path}: {e}")
            
    def get_session_summary(self) -> Dict[str, Any]:
        """Get a summary of the current session"""
        return {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "duration": str(datetime.now() - self.start_time),
            "workspace_path": str(self.workspace_path),
            "files_created": len(self.created_files),
            "files_modified": len(self.modified_files),
            "files_deleted": len(self.deleted_files),
            "total_files_tracked": len(self.created_files) + len(self.modified_files),
            "created_files": list(self.created_files),
            "modified_files": list(self.modified_files),
            "deleted_files": list(self.deleted_files)
        }
        
    def get_all_tracked_files(self) -> List[str]:
        """Get all files tracked in this session"""
        all_files = set()
        all_files.update(self.created_files)
        all_files.update(self.modified_files)
        # Don't include deleted files in active file list
        return list(all_files)
        
    def add_session_note(self, note: str):
        """Add a note to the session"""
        timestamp = datetime.now().isoformat()
        self.session_data["session_notes"].append({
            "timestamp": timestamp,
            "note": note
        })
        self._save_session()
        
    def cleanup_session(self, preserve_files: bool = True):
        """Clean up session data"""
        if not preserve_files:
            # Optionally remove session files
            session_file = self._get_session_file_path()
            if session_file.exists():
                try:
                    session_file.unlink()
                    logger.info("Removed session file")
                except Exception as e:
                    logger.warning(f"Failed to remove session file: {e}")
        else:
            # Just mark session as ended
            self.session_data["end_time"] = datetime.now().isoformat()
            self._save_session()
            
        logger.info(f"Session cleanup completed: {self.session_id}")
