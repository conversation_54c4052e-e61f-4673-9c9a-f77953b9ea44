"""
PyIDE Demo Script

This script demonstrates how to use PyIDE programmatically
and shows examples of the AI interaction workflow.
"""

import asyncio
import json
from pathlib import Path
import sys

# Add parent directory to path to import core modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.ai_client import AIClient
from core.parser import ResponseParser
from core.security.ignore import IgnoreController
from core.security.validator import SecurityValidator
from core.tools.file_ops import FileOperations


async def demo_ai_interaction():
    """Demonstrate basic AI interaction"""
    print("🤖 PyIDE AI Interaction Demo")
    print("=" * 50)
    
    # Load configuration
    config_path = Path(__file__).parent.parent / "config.json"
    with open(config_path) as f:
        config = json.load(f)
    
    # Initialize components
    workspace_path = Path(__file__).parent.parent
    
    # Create AI client
    async with AIClient(config) as ai_client:
        print("\n1. Simple AI Request")
        print("-" * 30)
        
        response = await ai_client.simple_request(
            "Explain what PyIDE is in one sentence.",
            str(workspace_path)
        )
        print(f"AI Response: {response}")
        
        print("\n2. Streaming AI Request")
        print("-" * 30)
        
        print("AI Response (streaming): ", end="")
        async for chunk in ai_client.stream_request(
            "List the main features of PyIDE in bullet points.",
            str(workspace_path)
        ):
            if chunk.type == "text":
                print(chunk.content, end="")
            elif chunk.type == "error":
                print(f"\nError: {chunk.error}")
                break
        print("\n")


async def demo_response_parsing():
    """Demonstrate response parsing"""
    print("\n🔍 Response Parsing Demo")
    print("=" * 50)
    
    # Sample AI response with tool calls
    sample_response = """I'll help you create a hello world script.

<write_to_file>
<path>hello.py</path>
<content>#!/usr/bin/env python3

def main():
    print("Hello, World!")
    print("Welcome to PyIDE!")

if __name__ == "__main__":
    main()
</content>
</write_to_file>

The script has been created successfully!"""

    # Parse the response
    parser = ResponseParser()
    content_blocks = parser.parse_complete(sample_response)
    
    print("Parsed Content Blocks:")
    for i, block in enumerate(content_blocks):
        print(f"\nBlock {i+1}: {block.type}")
        if hasattr(block, 'content'):
            print(f"Content: {block.content[:100]}...")
        elif hasattr(block, 'name'):
            print(f"Tool: {block.name}")
            print(f"Parameters: {list(block.params.keys())}")
    
    # Extract tool calls
    tool_calls = parser.extract_tool_calls(content_blocks)
    print(f"\nFound {len(tool_calls)} tool calls:")
    for tool_call in tool_calls:
        print(f"- {tool_call.name}: {list(tool_call.params.keys())}")


async def demo_file_operations():
    """Demonstrate file operations"""
    print("\n📁 File Operations Demo")
    print("=" * 50)
    
    # Load configuration
    config_path = Path(__file__).parent.parent / "config.json"
    with open(config_path) as f:
        config = json.load(f)
    
    workspace_path = Path(__file__).parent.parent
    
    # Initialize security components
    ignore_controller = IgnoreController(str(workspace_path))
    security_validator = SecurityValidator(config)
    
    # Initialize file operations
    file_ops = FileOperations(config, ignore_controller, security_validator)
    
    print("1. List Files Demo")
    print("-" * 20)
    
    result = await file_ops.list_files(".", recursive=False)
    if result["success"]:
        print(f"Found {len(result['files'])} files and {len(result['directories'])} directories")
        for file_info in result["files"][:5]:  # Show first 5 files
            print(f"  📄 {file_info['name']}")
    else:
        print(f"Error: {result['error']}")
    
    print("\n2. Read File Demo")
    print("-" * 20)
    
    # Try to read this demo file
    result = await file_ops.read_file("examples/demo.py")
    if result["success"]:
        content = result["content"]
        lines = content.split('\n')
        print(f"File has {len(lines)} lines")
        print("First 3 lines:")
        for i, line in enumerate(lines[:3]):
            print(f"  {i+1}: {line}")
    else:
        print(f"Error: {result['error']}")


async def demo_security_features():
    """Demonstrate security features"""
    print("\n🔒 Security Features Demo")
    print("=" * 50)
    
    workspace_path = Path(__file__).parent.parent
    
    # Initialize ignore controller
    ignore_controller = IgnoreController(str(workspace_path))
    
    print("1. Ignore Patterns")
    print("-" * 20)
    
    if ignore_controller.has_ignore_file():
        patterns = ignore_controller.get_ignore_patterns()
        print(f"Loaded {len(patterns)} ignore patterns:")
        for pattern in patterns[:5]:  # Show first 5
            print(f"  - {pattern}")
    else:
        print("No .pyideignore file found")
    
    print("\n2. Access Validation")
    print("-" * 20)
    
    test_paths = [
        "main.py",
        "__pycache__/test.pyc",
        ".git/config",
        "examples/demo.py",
        "config.json"
    ]
    
    for path in test_paths:
        allowed = ignore_controller.validate_access(path)
        status = "✅ ALLOWED" if allowed else "❌ BLOCKED"
        print(f"  {path}: {status}")


async def main():
    """Run all demos"""
    print("🚀 PyIDE Demonstration")
    print("=" * 60)
    
    try:
        await demo_ai_interaction()
        await demo_response_parsing()
        await demo_file_operations()
        await demo_security_features()
        
        print("\n✅ Demo completed successfully!")
        print("\nTo start PyIDE interactively, run:")
        print("  python main.py")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
