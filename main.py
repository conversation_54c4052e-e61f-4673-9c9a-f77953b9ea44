import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Core imports
from core.ai_client import <PERSON><PERSON>lient
from core.parser import <PERSON><PERSON><PERSON><PERSON>, Tool<PERSON><PERSON>, TextContent
from core.security.ignore import IgnoreController
from core.security.validator import SecurityValidator
from core.tools.file_ops import FileOperations
from core.tools.command import CommandExecutor
from core.tools.search import FileSearcher
from core.ui.cli import CLIInterface
from core.ui.diff_view import DiffViewer
from core.workspace_session import WorkspaceSession
from core.orchestration import ToolOrchestrator


class PyIDE:
    """
    Main PyIDE application class
    """

    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.workspace_path: Optional[Path] = None
        self.base_workspace_path: Optional[Path] = None

        # Core components
        self.ai_client: Optional[AIClient] = None
        self.parser: Optional[ResponseParser] = None
        self.ignore_controller: Optional[IgnoreController] = None
        self.security_validator: Optional[SecurityValidator] = None

        # Tools
        self.file_ops: Optional[FileOperations] = None
        self.command_executor: Optional[CommandExecutor] = None
        self.file_searcher: Optional[FileSearcher] = None

        # UI
        self.cli: Optional[CLIInterface] = None
        self.diff_viewer: Optional[DiffViewer] = None

        # State
        self.running = False
        self.workspace_session = None

        # Orchestration
        self.orchestrator: Optional[ToolOrchestrator] = None

    async def initialize(self):
        """Initialize all components"""
        try:
            # Load configuration
            await self._load_config()

            # Setup logging
            self._setup_logging()

            # Initialize workspace
            self._setup_workspace()

            # Initialize security components
            self._initialize_security()

            # Initialize tools
            self._initialize_tools()

            # Initialize orchestration
            self._initialize_orchestration()

            # Initialize UI
            self._initialize_ui()

            # Initialize AI client
            await self._initialize_ai_client()

            # Initialize parser
            self.parser = ResponseParser()

            logging.info("PyIDE initialized successfully")

        except Exception as e:
            logging.error(f"Failed to initialize PyIDE: {e}")
            raise

    async def _load_config(self):
        """Load configuration from file"""
        config_file = Path(self.config_path)

        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")

    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get("logging", {})

        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_config.get("file", "pyide.log"))
            ]
        )

    def _setup_workspace(self):
        """Setup workspace directory and temporary workspace if enabled"""
        workspace_config = self.config.get("workspace", {})
        workspace_dir = workspace_config.get("default_directory", ".")

        # Setup base workspace
        base_workspace_path = Path(workspace_dir).resolve()
        if not base_workspace_path.exists():
            base_workspace_path.mkdir(parents=True, exist_ok=True)

        # Setup temporary workspace if enabled
        temp_config = workspace_config.get("temp_workspace", {})
        if temp_config.get("enabled", True):
            temp_dir_name = temp_config.get("directory_name", "temp")
            temp_workspace_path = base_workspace_path / temp_dir_name

            # Create temp directory if auto_create is enabled
            if temp_config.get("auto_create", True):
                if not temp_workspace_path.exists():
                    temp_workspace_path.mkdir(parents=True, exist_ok=True)
                    logging.info(f"Created temporary workspace: {temp_workspace_path}")

                # Create default .pyideignore in temp workspace if it doesn't exist
                self._create_default_ignore_file(temp_workspace_path)

            # Use temp workspace as the primary workspace for AI operations
            self.workspace_path = temp_workspace_path
            self.base_workspace_path = base_workspace_path

            # Initialize workspace session for temp workspace
            self.workspace_session = WorkspaceSession(self.workspace_path)

            logging.info(f"Workspace: {self.workspace_path} (temp)")
            logging.info(f"Base workspace: {self.base_workspace_path}")
            logging.info(f"Session: {self.workspace_session.session_id}")
        else:
            # Use base workspace if temp workspace is disabled
            self.workspace_path = base_workspace_path
            self.base_workspace_path = base_workspace_path
            logging.info(f"Workspace: {self.workspace_path}")

    def _create_default_ignore_file(self, workspace_path: Path):
        """Create a default .pyideignore file in the workspace if it doesn't exist"""
        ignore_file_name = self.config.get("security", {}).get("ignore_file", ".pyideignore")
        ignore_file_path = workspace_path / ignore_file_name

        if not ignore_file_path.exists():
            default_ignore_content = """# PyIDE Ignore File
# This file specifies patterns for files and directories to ignore
# Uses gitignore syntax

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
*.tmp
*.temp

# Logs
*.log
logs/

# Temporary files
temp/
tmp/
"""
            try:
                with open(ignore_file_path, 'w', encoding='utf-8') as f:
                    f.write(default_ignore_content)
                logging.info(f"Created default ignore file: {ignore_file_path}")
            except Exception as e:
                logging.warning(f"Failed to create default ignore file: {e}")

    def _initialize_security(self):
        """Initialize security components"""
        ignore_file = self.config.get("security", {}).get("ignore_file", ".pyideignore")

        self.ignore_controller = IgnoreController(
            str(self.workspace_path),
            ignore_file
        )

        self.security_validator = SecurityValidator(self.config)

    def _initialize_tools(self):
        """Initialize tool components"""
        self.file_ops = FileOperations(
            self.config,
            self.ignore_controller,
            self.security_validator,
            workspace_path=self.workspace_path,
            workspace_session=self.workspace_session
        )

        self.command_executor = CommandExecutor(
            self.config,
            self.ignore_controller,
            self.security_validator,
            workspace_path=self.workspace_path
        )

        self.file_searcher = FileSearcher(
            self.config,
            self.ignore_controller,
            workspace_path=self.workspace_path
        )

    def _initialize_orchestration(self):
        """Initialize orchestration system"""
        orchestration_config = self.config.get("orchestration", {})
        self.orchestrator = ToolOrchestrator(orchestration_config)

        # Register tool executors
        self._register_tool_executors()

        # Add progress handler
        self.orchestrator.add_progress_handler(self._handle_progress_event)

    def _register_tool_executors(self):
        """Register all tool executors with the orchestrator"""
        # File operations
        self.orchestrator.register_tool_executor("read_file", self._execute_read_file)
        self.orchestrator.register_tool_executor("write_to_file", self._execute_write_file)
        self.orchestrator.register_tool_executor("replace_in_file", self._execute_replace_file)
        self.orchestrator.register_tool_executor("create_directory", self._execute_create_directory)

        # File management
        self.orchestrator.register_tool_executor("list_files", self._execute_list_files)
        self.orchestrator.register_tool_executor("search_files", self._execute_search_files)

        # Command execution
        self.orchestrator.register_tool_executor("execute_command", self._execute_command)

        # Interactive
        self.orchestrator.register_tool_executor("ask_followup_question", self._execute_ask_question)

    def _handle_progress_event(self, event):
        """Handle progress events from orchestrator"""
        # Display progress information to user
        if hasattr(self.cli, 'display_progress'):
            self.cli.display_progress(event)
        else:
            # Fallback to info display
            self.cli.display_info(f"Progress: {event.message}")

    def _initialize_ui(self):
        """Initialize UI components"""
        self.cli = CLIInterface(self.config)
        self.diff_viewer = DiffViewer(self.config)

    async def _initialize_ai_client(self):
        """Initialize AI client"""
        self.ai_client = AIClient(self.config)

    # Tool executor wrapper methods for orchestration
    async def _execute_read_file(self, path: str, **kwargs) -> Dict[str, Any]:
        """Execute read_file tool"""
        return await self.file_ops.read_file(path)

    async def _execute_write_file(self, path: str, content: str, **kwargs) -> Dict[str, Any]:
        """Execute write_to_file tool"""
        return await self.file_ops.write_file(path, content)

    async def _execute_replace_file(self, path: str, diff: str, **kwargs) -> Dict[str, Any]:
        """Execute replace_in_file tool"""
        return await self.file_ops.replace_in_file(path, diff)

    async def _execute_create_directory(self, path: str, **kwargs) -> Dict[str, Any]:
        """Execute create_directory tool"""
        return await self.file_ops.create_directory(path, require_approval=False)

    async def _execute_list_files(self, path: str = ".", recursive: str = "false", **kwargs) -> Dict[str, Any]:
        """Execute list_files tool"""
        recursive_bool = recursive.lower() in ["true", "1", "yes"]
        return await self.file_ops.list_files(path, recursive=recursive_bool)

    async def _execute_search_files(self, path: str, regex: str, file_pattern: str = "*", **kwargs) -> Dict[str, Any]:
        """Execute search_files tool"""
        return await self.file_searcher.search_files(path, regex, file_pattern)

    async def _execute_command(self, command: str, **kwargs) -> Dict[str, Any]:
        """Execute execute_command tool"""
        return await self.command_executor.execute_command(command)

    async def _execute_ask_question(self, question: str, **kwargs) -> Dict[str, Any]:
        """Execute ask_followup_question tool"""
        self.cli.display_info(f"AI Question: {question}")
        answer = self.cli.get_user_input()
        return {"success": True, "answer": answer}

    async def run(self):
        """Main application loop"""
        self.running = True

        try:
            # Display welcome message
            self.cli.display_welcome()

            # Main interaction loop
            while self.running:
                try:
                    # Get user input
                    user_input = self.cli.get_user_input()

                    if not user_input.strip():
                        continue

                    # Handle special commands
                    if user_input.startswith('/'):
                        await self._handle_special_command(user_input)
                        continue

                    # Process AI request
                    await self._process_ai_request(user_input)

                except KeyboardInterrupt:
                    self.cli.display_info("Use /exit to quit")
                    continue
                except Exception as e:
                    self.cli.display_error(f"Unexpected error: {e}")
                    logging.error(f"Unexpected error in main loop: {e}")
                    continue

        except Exception as e:
            self.cli.display_error(f"Fatal error: {e}")
            logging.error(f"Fatal error: {e}")
        finally:
            await self._cleanup()

    async def _handle_special_command(self, command: str):
        """Handle special commands like /help, /exit, etc."""
        command = command.lower().strip()

        if command == "/exit" or command == "/quit":
            self.cli.display_info("Goodbye!")
            self.running = False
        elif command == "/help":
            self.cli.display_help()
        elif command == "/clear":
            self.cli.clear_screen()
            if self.ai_client:
                self.ai_client.clear_history()
            self.cli.display_success("Conversation history cleared")
        elif command == "/status":
            await self._show_status()
        elif command == "/config":
            await self._show_config()
        else:
            self.cli.display_error(f"Unknown command: {command}")

    async def _process_ai_request(self, user_input: str):
        """Process AI request and handle tool calls"""
        try:
            self.cli.display_ai_response_start()

            # Stream AI response
            async with self.ai_client as client:
                full_response = ""

                async for chunk in client.stream_request(user_input, str(self.workspace_path)):
                    if chunk.type == "text":
                        self.cli.display_ai_text(chunk.content)
                        full_response += chunk.content
                    elif chunk.type == "error":
                        self.cli.display_error(f"AI Error: {chunk.error}")
                        return
                    elif chunk.type == "usage":
                        # Log token usage
                        usage = chunk.usage
                        logging.info(f"Token usage: {usage}")

                # Parse response for tool calls
                content_blocks = self.parser.parse_complete(full_response)
                tool_calls = self.parser.extract_tool_calls(content_blocks)

                # Check if orchestration should be used
                if self.orchestrator and self.config.get("orchestration", {}).get("enabled", True):
                    # Use orchestrator for intelligent multi-step execution
                    orchestration_result = await self.orchestrator.analyze_and_execute(user_input, tool_calls)

                    if orchestration_result["success"]:
                        if orchestration_result.get("is_multi_step", False):
                            self.cli.display_info("Completed multi-step operation")
                        # Tool results are already displayed via progress events
                    else:
                        self.cli.display_error(f"Orchestration failed: {orchestration_result.get('error', 'Unknown error')}")
                else:
                    # Fall back to original sequential execution
                    for tool_call in tool_calls:
                        if self.parser.validate_tool_call(tool_call):
                            await self._execute_tool_call(tool_call)
                        else:
                            self.cli.display_error(f"Invalid tool call: {tool_call.name}")

        except Exception as e:
            self.cli.display_error(f"Error processing AI request: {e}")
            logging.error(f"Error processing AI request: {e}")

    async def _execute_tool_call(self, tool_call: ToolUse):
        """Execute a tool call"""
        try:
            self.cli.display_tool_call(tool_call.name, tool_call.params)

            result = None
            success = False

            # Execute based on tool name
            if tool_call.name == "read_file":
                result = await self.file_ops.read_file(tool_call.params.get("path", ""))
                success = result.get("success", False)

            elif tool_call.name == "write_to_file":
                result = await self.file_ops.write_file(
                    tool_call.params.get("path", ""),
                    tool_call.params.get("content", "")
                )
                success = result.get("success", False)

            elif tool_call.name == "replace_in_file":
                result = await self.file_ops.replace_in_file(
                    tool_call.params.get("path", ""),
                    tool_call.params.get("diff", "")
                )
                success = result.get("success", False)

            elif tool_call.name == "list_files":
                recursive = tool_call.params.get("recursive", "false").lower() == "true"
                result = await self.file_ops.list_files(
                    tool_call.params.get("path", "."),
                    recursive=recursive
                )
                success = result.get("success", False)

            elif tool_call.name == "search_files":
                result = await self.file_searcher.search_files(
                    tool_call.params.get("path", "."),
                    tool_call.params.get("regex", ""),
                    tool_call.params.get("file_pattern", "*")
                )
                success = result.get("success", False)

            elif tool_call.name == "execute_command":
                result = await self.command_executor.execute_command(
                    tool_call.params.get("command", "")
                )
                success = result.get("success", False)

            elif tool_call.name == "ask_followup_question":
                question = tool_call.params.get("question", "")
                self.cli.display_info(f"AI Question: {question}")
                answer = self.cli.get_user_input()
                result = {"success": True, "answer": answer}
                success = True

            else:
                result = {"success": False, "error": f"Unknown tool: {tool_call.name}"}
                success = False

            # Display result
            if result:
                self.cli.display_tool_result(tool_call.name, result, success)

                # Add tool result to conversation history
                if self.ai_client:
                    tool_result_text = self.parser.format_tool_result(
                        tool_call.name,
                        result.get("content", str(result)),
                        success
                    )
                    self.ai_client.add_message("user", tool_result_text)

        except Exception as e:
            self.cli.display_error(f"Error executing tool {tool_call.name}: {e}")
            logging.error(f"Error executing tool {tool_call.name}: {e}")

    async def _show_status(self):
        """Show system status"""
        status_info = {
            "Workspace": {
                "status": "ok" if self.workspace_path and self.workspace_path.exists() else "error",
                "details": str(self.workspace_path) if self.workspace_path else "Not set"
            },
            "AI Client": {
                "status": "ok" if self.ai_client else "error",
                "details": self.config.get("api", {}).get("model", "Not configured")
            },
            "Security": {
                "status": "ok" if self.ignore_controller else "error",
                "details": f"Ignore file: {self.ignore_controller.has_ignore_file()}" if self.ignore_controller else "Not loaded"
            },
            "Tools": {
                "status": "ok" if all([self.file_ops, self.command_executor, self.file_searcher]) else "error",
                "details": "All tools loaded" if all([self.file_ops, self.command_executor, self.file_searcher]) else "Not loaded"
            }
        }

        self.cli.display_status(status_info)

    async def _show_config(self):
        """Show current configuration"""
        # Redact sensitive information
        safe_config = self.config.copy()
        if "api" in safe_config and "key" in safe_config["api"]:
            key = safe_config["api"]["key"]
            safe_config["api"]["key"] = key[:10] + "..." + key[-4:] if len(key) > 14 else "***"

        config_text = json.dumps(safe_config, indent=2)
        self.cli.console.print(f"[bold]Current Configuration:[/bold]\n{config_text}")

    async def _cleanup(self):
        """Cleanup resources"""
        if self.ai_client:
            # AI client cleanup is handled by context manager
            pass

        # Cleanup workspace session
        if self.workspace_session:
            preserve_files = self.config.get("workspace", {}).get("temp_workspace", {}).get("preserve_on_exit", True)
            self.workspace_session.cleanup_session(preserve_files=preserve_files)

        logging.info("PyIDE shutdown complete")


async def main():
    """Main entry point"""
    try:
        app = PyIDE()
        await app.initialize()
        await app.run()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())