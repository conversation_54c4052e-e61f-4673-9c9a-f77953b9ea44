2025-07-25 09:58:56,083 - core.workspace_session - INFO - Loaded existing session: session_20250725_095856_7061fa83
2025-07-25 09:58:56,084 - root - INFO - Workspace: C:\Users\<USER>\OneDrive\Desktop\pyIDE\temp (temp)
2025-07-25 09:58:56,084 - root - INFO - Base workspace: C:\Users\<USER>\OneDrive\Desktop\pyIDE
2025-07-25 09:58:56,084 - root - INFO - Session: session_20250725_095856_7061fa83
2025-07-25 09:58:56,086 - core.security.ignore - INFO - Loaded 38 ignore patterns from C:\Users\<USER>\OneDrive\Desktop\pyIDE\temp\.pyideignore
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: read_file
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: write_to_file
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: replace_in_file
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: create_directory
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: list_files
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: search_files
2025-07-25 09:58:56,086 - core.orchestration.orchestrator - INFO - Registered tool executor: execute_command
2025-07-25 09:58:56,087 - core.orchestration.orchestrator - INFO - Registered tool executor: ask_followup_question
2025-07-25 09:58:56,089 - root - INFO - PyIDE initialized successfully
2025-07-25 09:59:20,478 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 09:59:22,009 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 09:59:24,534 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 09:59:29,204 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 09:59:30,454 - core.workspace_session - INFO - Session cleanup completed: session_20250725_095856_7061fa83
2025-07-25 09:59:30,454 - root - INFO - PyIDE shutdown complete
2025-07-25 10:02:43,493 - core.workspace_session - INFO - Loaded existing session: session_20250725_100243_fe61d14e
2025-07-25 10:02:43,494 - root - INFO - Workspace: C:\Users\<USER>\OneDrive\Desktop\pyIDE\temp (temp)
2025-07-25 10:02:43,494 - root - INFO - Base workspace: C:\Users\<USER>\OneDrive\Desktop\pyIDE
2025-07-25 10:02:43,494 - root - INFO - Session: session_20250725_100243_fe61d14e
2025-07-25 10:02:43,496 - core.security.ignore - INFO - Loaded 38 ignore patterns from C:\Users\<USER>\OneDrive\Desktop\pyIDE\temp\.pyideignore
2025-07-25 10:02:43,496 - core.orchestration.orchestrator - INFO - Registered tool executor: read_file
2025-07-25 10:02:43,496 - core.orchestration.orchestrator - INFO - Registered tool executor: write_to_file
2025-07-25 10:02:43,496 - core.orchestration.orchestrator - INFO - Registered tool executor: replace_in_file
2025-07-25 10:02:43,497 - core.orchestration.orchestrator - INFO - Registered tool executor: create_directory
2025-07-25 10:02:43,497 - core.orchestration.orchestrator - INFO - Registered tool executor: list_files
2025-07-25 10:02:43,497 - core.orchestration.orchestrator - INFO - Registered tool executor: search_files
2025-07-25 10:02:43,497 - core.orchestration.orchestrator - INFO - Registered tool executor: execute_command
2025-07-25 10:02:43,497 - core.orchestration.orchestrator - INFO - Registered tool executor: ask_followup_question
2025-07-25 10:02:43,502 - root - INFO - PyIDE initialized successfully
2025-07-25 10:04:45,627 - core.tools.file_ops - INFO - Listed directory: . (2 files, 0 dirs)
2025-07-25 10:05:20,073 - core.workspace_session - INFO - Session cleanup completed: session_20250725_100243_fe61d14e
2025-07-25 10:05:20,073 - root - INFO - PyIDE shutdown complete
2025-07-25 10:10:34,357 - core.workspace_session - INFO - Loaded existing session: session_20250725_101034_1b7f58c2
2025-07-25 10:10:34,357 - root - INFO - Workspace: C:\Users\<USER>\OneDrive\Desktop\pyIDE\temp (temp)
2025-07-25 10:10:34,357 - root - INFO - Base workspace: C:\Users\<USER>\OneDrive\Desktop\pyIDE
2025-07-25 10:10:34,357 - root - INFO - Session: session_20250725_101034_1b7f58c2
2025-07-25 10:10:34,360 - core.security.ignore - INFO - Loaded 38 ignore patterns from C:\Users\<USER>\OneDrive\Desktop\pyIDE\temp\.pyideignore
2025-07-25 10:10:34,360 - core.orchestration.orchestrator - INFO - Registered tool executor: read_file
2025-07-25 10:10:34,360 - core.orchestration.orchestrator - INFO - Registered tool executor: write_to_file
2025-07-25 10:10:34,360 - core.orchestration.orchestrator - INFO - Registered tool executor: replace_in_file
2025-07-25 10:10:34,361 - core.orchestration.orchestrator - INFO - Registered tool executor: create_directory
2025-07-25 10:10:34,361 - core.orchestration.orchestrator - INFO - Registered tool executor: list_files
2025-07-25 10:10:34,361 - core.orchestration.orchestrator - INFO - Registered tool executor: search_files
2025-07-25 10:10:34,361 - core.orchestration.orchestrator - INFO - Registered tool executor: execute_command
2025-07-25 10:10:34,361 - core.orchestration.orchestrator - INFO - Registered tool executor: ask_followup_question
2025-07-25 10:10:34,363 - root - INFO - PyIDE initialized successfully
2025-07-25 10:10:52,103 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:10:53,737 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:10:56,733 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:11:01,244 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:11:36,820 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Provider returned error","code":429,"metadata":{"raw":"qwen/qwen3-coder:free is temporarily rate-limited upstream. Please retry shortly, or add your own key to accumulate your rate limits: https://openrouter.ai/settings/integrations","provider_name":"Chutes"}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:14:26,884 - core.workspace_session - INFO - Tracked file creation: enhanced_hello.py
2025-07-25 10:14:26,885 - core.tools.file_ops - INFO - Successfully wrote file: enhanced_hello.py (3867 chars)
2025-07-25 10:15:04,947 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:15:06,450 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:15:08,943 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:15:13,462 - core.ai_client - ERROR - API request failed: 429 - {"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753488000000"},"provider_name":null}},"user_id":"user_2yf4wzHgO0FfhSv4OMSKTqJ4dAK"}
2025-07-25 10:18:29,162 - core.workspace_session - INFO - Tracked file creation: gui_image_viewer.py
2025-07-25 10:18:29,162 - core.tools.file_ops - INFO - Successfully wrote file: gui_image_viewer.py (8324 chars)
