#!/usr/bin/env python3
"""
PyIDE Startup Script

This script provides an easy way to start PyIDE with proper error handling
and environment setup.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'aiohttp',
        'rich',
        'click',
        'pathspec',
        'chardet',
        'httpx'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install dependencies:")
        print("  pip install -r requirements.txt")
        return False
    
    return True

def check_config():
    """Check if configuration file exists"""
    config_file = current_dir / "config.json"
    
    if not config_file.exists():
        print("❌ Configuration file not found: config.json")
        print("\nPlease ensure config.json exists in the PyIDE directory.")
        return False
    
    try:
        import json
        with open(config_file) as f:
            config = json.load(f)
            
        # Check API configuration
        api_config = config.get("api", {})
        if not api_config.get("key"):
            print("⚠️  Warning: No API key configured in config.json")
            print("   Some features may not work without a valid API key.")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in config.json: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading config.json: {e}")
        return False

def setup_environment():
    """Setup environment variables and paths"""
    # Set environment variables for better error messages
    os.environ["PYTHONUNBUFFERED"] = "1"
    
    # Create logs directory if it doesn't exist
    logs_dir = current_dir / "logs"
    logs_dir.mkdir(exist_ok=True)

async def main():
    """Main startup function"""
    print("🚀 Starting PyIDE...")
    print("=" * 50)
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ Dependencies OK")
    
    # Check configuration
    print("⚙️  Checking configuration...")
    if not check_config():
        sys.exit(1)
    print("✅ Configuration OK")
    
    # Setup environment
    print("🔧 Setting up environment...")
    setup_environment()
    print("✅ Environment OK")
    
    print("\n🎯 Initializing PyIDE...")
    
    try:
        # Import and run main application
        from main import PyIDE
        
        app = PyIDE()
        await app.initialize()
        await app.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all PyIDE modules are properly installed.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)
