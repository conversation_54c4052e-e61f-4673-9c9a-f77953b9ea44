#!/usr/bin/env python3
"""
Enhanced Hello World Script

This script demonstrates several Python concepts:
- Command-line argument parsing
- Logging
- Configuration management
- Error handling
- Interactive input
"""

import argparse
import logging
import sys
from datetime import datetime
from typing import Optional


def setup_logging(log_level: str = "INFO") -> None:
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper(), logging.INFO),
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )


def get_greeting(name: Optional[str] = None, language: str = "en") -> str:
    """
    Generate a greeting based on language and name.
    
    Args:
        name: Person's name (optional)
        language: Language code (en, es, fr, de)
        
    Returns:
        Greeting string
    """
    greetings = {
        "en": "Hello",
        "es": "Ho<PERSON>",
        "fr": "Bonjour",
        "de": "<PERSON>o"
    }
    
    greeting = greetings.get(language.lower(), greetings["en"])
    
    if name:
        return f"{greeting}, {name}!"
    else:
        return f"{greeting}, World!"


def main() -> int:
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Enhanced Hello World Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhanced_hello.py
  python enhanced_hello.py --name Alice
  python enhanced_hello.py --name Bob --language es
  python enhanced_hello.py --log-level debug
        """
    )
    
    parser.add_argument(
        "--name", "-n",
        help="Name to greet",
        type=str
    )
    
    parser.add_argument(
        "--language", "-l",
        help="Language for greeting (en, es, fr, de)",
        choices=["en", "es", "fr", "de"],
        default="en"
    )
    
    parser.add_argument(
        "--log-level",
        help="Set the logging level",
        choices=["debug", "info", "warning", "error"],
        default="info"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        help="Interactive mode",
        action="store_true"
    )
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.log_level)
    logging.info("Starting enhanced hello world script")
    
    try:
        # Handle interactive mode
        if args.interactive:
            print("=== Enhanced Hello World Interactive Mode ===")
            name = input("What's your name? (Press Enter for 'World'): ").strip() or None
            if name:
                args.name = name
            
            print("Available languages: en (English), es (Spanish), fr (French), de (German)")
            lang = input("Choose language (default: en): ").strip() or "en"
            if lang in ["en", "es", "fr", "de"]:
                args.language = lang
            else:
                print(f"Unknown language '{lang}', using English")
                args.language = "en"
        
        # Generate and print greeting
        greeting = get_greeting(args.name, args.language)
        print(greeting)
        
        # Log the greeting
        logging.info(f"Greeting displayed: {greeting}")
        
        # Print additional information
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\nCurrent time: {current_time}")
        print(f"Python version: {sys.version.split()[0]}")
        
        logging.info("Script completed successfully")
        return 0
        
    except KeyboardInterrupt:
        print("\nScript interrupted by user")
        logging.info("Script interrupted by user")
        return 1
    except Exception as e:
        logging.error(f"An error occurred: {e}")
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())