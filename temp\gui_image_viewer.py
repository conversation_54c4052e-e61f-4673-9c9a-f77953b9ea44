#!/usr/bin/env python3
"""
Cool GUI Image Viewer with PySimpleGUI

Features:
- Browse and view images
- Basic image editing (flip, rotate)
- Slideshow mode
- Image information display
"""

import PySimpleGUI as sg
import os
from PIL import Image, ImageTk
import io
import threading
from typing import Optional, Tuple

class ImageViewer:
    def __init__(self):
        self.current_image: Optional[Image.Image] = None
        self.current_file: str = ""
        self.image_files: list = []
        self.current_index: int = 0
        self.slideshow_active: bool = False
        self.slideshow_delay: int = 2000  # milliseconds
        
        # Set up the theme
        sg.theme('DarkBlue3')
        
    def load_image(self, file_path: str) -> Optional[Image.Image]:
        """Load an image from file path."""
        try:
            image = Image.open(file_path)
            self.current_file = file_path
            return image
        except Exception as e:
            sg.popup_error(f"Error loading image: {e}")
            return None
    
    def resize_image(self, image: Image.Image, size: Tuple[int, int]) -> Image.Image:
        """Resize image to fit within specified size while maintaining aspect ratio."""
        image.thumbnail(size, Image.LANCZOS)
        return image
    
    def image_to_data(self, image: Image.Image) -> bytes:
        """Convert PIL Image to bytes for GUI display."""
        bio = io.BytesIO()
        image.save(bio, format="PNG")
        return bio.getvalue()
    
    def get_image_files(self, folder: str) -> list:
        """Get all image files in a folder."""
        extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff')
        files = [f for f in os.listdir(folder) if f.lower().endswith(extensions)]
        return sorted([os.path.join(folder, f) for f in files])
    
    def update_image_info(self, image: Image.Image, file_path: str) -> str:
        """Generate image information string."""
        if not image:
            return "No image loaded"
            
        file_size = os.path.getsize(file_path)
        file_size_str = f"{file_size/1024:.1f} KB" if file_size < 1024*1024 else f"{file_size/(1024*1024):.1f} MB"
        
        return f"""
File: {os.path.basename(file_path)}
Size: {image.width} x {image.height}
Mode: {image.mode}
File Size: {file_size_str}
        """.strip()
    
    def slideshow(self):
        """Run slideshow in a separate thread."""
        while self.slideshow_active and self.image_files:
            self.current_index = (self.current_index + 1) % len(self.image_files)
            self.load_and_display_image(self.image_files[self.current_index])
            sg.popup_auto_close('', auto_close_duration=self.slideshow_delay/1000, no_titlebar=True, keep_on_top=True)
    
    def load_and_display_image(self, file_path: str):
        """Load and display an image."""
        self.current_image = self.load_image(file_path)
        if self.current_image:
            # Update image display
            resized_image = self.resize_image(self.current_image.copy(), (600, 400))
            image_data = self.image_to_data(resized_image)
            self.window['-IMAGE-'].update(data=image_data)
            
            # Update image info
            info_text = self.update_image_info(self.current_image, file_path)
            self.window['-INFO-'].update(info_text)
            
            # Update file list selection
            self.window['-FILELIST-'].update(set_to_index=self.image_files.index(file_path))
    
    def create_layout(self):
        """Create the GUI layout."""
        file_list_column = [
            [sg.Text("Folder:"), sg.In(size=(25, 1), enable_events=True, key="-FOLDER-"),
             sg.FolderBrowse()],
            [sg.Listbox(values=[], enable_events=True, size=(40, 20), key="-FILELIST-")]
        ]
        
        image_viewer_column = [
            [sg.Text("Image Viewer", size=(40, 1), justification="center", font=("Helvetica", 16))],
            [sg.Image(key="-IMAGE-", size=(600, 400))],
            [sg.Text(size=(60, 5), key="-INFO-", font=("Courier", 10))],
            [sg.HorizontalSeparator()],
            [sg.Button("Previous"), sg.Button("Next"), 
             sg.Button("Flip Horizontal"), sg.Button("Flip Vertical"),
             sg.Button("Rotate Left"), sg.Button("Rotate Right"),
             sg.Button("Slideshow"), sg.Button("Stop Slideshow")]
        ]
        
        layout = [
            [sg.Column(file_list_column), sg.VSeparator(), sg.Column(image_viewer_column)]
        ]
        
        return layout
    
    def run(self):
        """Run the GUI application."""
        layout = self.create_layout()
        self.window = sg.Window("Cool Image Viewer", layout, resizable=True, finalize=True)
        
        # Event loop
        while True:
            event, values = self.window.read(timeout=100)
            if event == sg.WIN_CLOSED:
                break
            
            # Folder selected
            if event == "-FOLDER-":
                folder = values["-FOLDER-"]
                if os.path.isdir(folder):
                    self.image_files = self.get_image_files(folder)
                    self.window["-FILELIST-"].update(self.image_files)
                    if self.image_files:
                        self.current_index = 0
                        self.load_and_display_image(self.image_files[0])
            
            # File selected from list
            if event == "-FILELIST-" and values["-FILELIST-"]:
                file_path = values["-FILELIST-"][0]
                self.current_index = self.image_files.index(file_path)
                self.load_and_display_image(file_path)
            
            # Navigation buttons
            if event == "Previous" and self.image_files:
                self.current_index = (self.current_index - 1) % len(self.image_files)
                self.load_and_display_image(self.image_files[self.current_index])
                
            if event == "Next" and self.image_files:
                self.current_index = (self.current_index + 1) % len(self.image_files)
                self.load_and_display_image(self.image_files[self.current_index])
            
            # Image editing
            if event in ["Flip Horizontal", "Flip Vertical"] and self.current_image:
                if event == "Flip Horizontal":
                    self.current_image = self.current_image.transpose(Image.FLIP_LEFT_RIGHT)
                else:
                    self.current_image = self.current_image.transpose(Image.FLIP_TOP_BOTTOM)
                
                # Update display
                resized_image = self.resize_image(self.current_image.copy(), (600, 400))
                image_data = self.image_to_data(resized_image)
                self.window['-IMAGE-'].update(data=image_data)
            
            if event in ["Rotate Left", "Rotate Right"] and self.current_image:
                if event == "Rotate Left":
                    self.current_image = self.current_image.rotate(90, expand=True)
                else:
                    self.current_image = self.current_image.rotate(-90, expand=True)
                
                # Update display
                resized_image = self.resize_image(self.current_image.copy(), (600, 400))
                image_data = self.image_to_data(resized_image)
                self.window['-IMAGE-'].update(data=image_data)
                
                # Update info
                info_text = self.update_image_info(self.current_image, self.current_file)
                self.window['-INFO-'].update(info_text)
            
            # Slideshow
            if event == "Slideshow":
                if self.image_files:
                    self.slideshow_active = True
                    threading.Thread(target=self.slideshow, daemon=True).start()
                else:
                    sg.popup("No images to show in slideshow")
            
            if event == "Stop Slideshow":
                self.slideshow_active = False
        
        self.window.close()

def main():
    """Main function."""
    try:
        viewer = ImageViewer()
        viewer.run()
    except Exception as e:
        sg.popup_error(f"Application error: {e}")

if __name__ == "__main__":
    main()