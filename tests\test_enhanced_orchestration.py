"""
Tests for Enhanced Multi-Step Tool Execution

This module tests the enhanced orchestration capabilities:
- Enhanced request analysis and multi-step detection
- Dynamic plan generation and adaptation
- Improved error recovery and continuation
- Enhanced progress reporting
- Tool call batching and optimization
"""

import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import pytest

# Import the modules to test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.orchestration import (
    ToolOrchestrator, ExecutionPlan, ExecutionStep, ExecutionMode,
    RequestAnalyzer, DynamicPlanner
)
from core.orchestration.dynamic_planner import PlanModification, PlanModificationType
from core.parser import ResponseParser, ToolUse


class TestEnhancedRequestAnalyzer:
    """Test enhanced request analysis functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.analyzer = RequestAnalyzer()
        
    def test_enhanced_multi_step_detection(self):
        """Test enhanced multi-step detection with numerical indicators"""
        result = self.analyzer.analyze_request("create 3 Python files for a web application")
        
        assert result["is_multi_step"]
        assert result["has_numerical_indicators"]
        assert len(result["numerical_indicators"]) > 0
        assert result["estimated_steps"] >= 3
        
    def test_multiple_action_detection(self):
        """Test detection of multiple actions in a request"""
        result = self.analyzer.analyze_request("read the config file and then create a backup")
        
        assert result["is_multi_step"]
        assert result["has_multiple_actions"]
        assert len(result["found_verbs"]) >= 2
        assert "read" in result["found_verbs"]
        assert "create" in result["found_verbs"]
        
    def test_implicit_multi_step_detection(self):
        """Test detection of implicit multi-step operations"""
        result = self.analyzer.analyze_request("refactor the entire project structure")
        
        assert result["is_multi_step"]
        assert result["confidence"] > 0.5
        
    def test_project_wide_operation_detection(self):
        """Test detection of project-wide operations"""
        result = self.analyzer.analyze_request("analyze all Python files in the workspace")
        
        assert result["is_multi_step"]
        assert result["estimated_complexity"] in ["medium", "high"]
        assert "list_files" in result["suggested_tools"]
        assert "read_file" in result["suggested_tools"]


class TestDynamicPlanner:
    """Test dynamic planning functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.planner = DynamicPlanner()
        
    def test_result_handler_registration(self):
        """Test registration of result handlers"""
        def mock_handler(plan, step, result):
            return []
            
        self.planner.register_result_handler("test_tool", mock_handler)
        assert "test_tool" in self.planner.result_handlers
        
    def test_plan_adaptation_list_files(self):
        """Test plan adaptation based on list_files result"""
        plan = ExecutionPlan()
        step = ExecutionStep("step1", "list_files", {"path": "."}, set())
        result = {"success": True, "files": ["file1.py", "file2.py", "file3.py"]}
        
        modifications = self.planner.adapt_plan_based_on_result(plan, "step1", result)
        
        # Should create read_file steps for discovered files
        read_modifications = [m for m in modifications if m.new_step and m.new_step.tool_name == "read_file"]
        assert len(read_modifications) == 3
        
    def test_plan_adaptation_search_files(self):
        """Test plan adaptation based on search_files result"""
        plan = ExecutionPlan()
        step = ExecutionStep("step1", "search_files", {"path": ".", "regex": "test"}, set())
        result = {
            "success": True, 
            "matches": [
                {"file": "test1.py", "line": 1},
                {"file": "test2.py", "line": 5}
            ]
        }
        
        modifications = self.planner.adapt_plan_based_on_result(plan, "step1", result)
        
        # Should create read_file steps for matching files
        read_modifications = [m for m in modifications if m.new_step and m.new_step.tool_name == "read_file"]
        assert len(read_modifications) == 2
        
    def test_conditional_operations(self):
        """Test conditional operations based on file content"""
        plan = ExecutionPlan()
        step = ExecutionStep("step1", "read_file", {"path": "script.py"}, set())
        result = {
            "success": True,
            "content": "#!/usr/bin/env python\nprint('Hello')\nif __name__ == '__main__':\n    main()"
        }
        
        modifications = self.planner.adapt_plan_based_on_result(plan, "step1", result)
        
        # Should suggest running the script
        run_modifications = [m for m in modifications if m.new_step and m.new_step.tool_name == "execute_command"]
        assert len(run_modifications) == 1
        assert "python script.py" in run_modifications[0].new_step.params["command"]
        
    def test_modification_application(self):
        """Test application of modifications to a plan"""
        plan = ExecutionPlan()
        original_step = ExecutionStep("step1", "list_files", {"path": "."}, set())
        plan.add_step(original_step)
        
        # Create a modification to add a new step
        new_step = ExecutionStep("step2", "read_file", {"path": "test.py"}, {"step1"})
        modification = PlanModification(
            modification_type=PlanModificationType.ADD_STEP,
            new_step=new_step,
            reason="Test modification"
        )
        
        success = self.planner.apply_modifications(plan, [modification])
        
        assert success
        assert len(plan) == 2
        assert "step2" in plan


class TestEnhancedOrchestrator:
    """Test enhanced orchestrator functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        config = {
            "enable_dynamic_planning": True,
            "continue_on_failure": False,
            "max_parallel_executions": 3
        }
        self.orchestrator = ToolOrchestrator(config)
        
        # Register mock tool executors
        self.orchestrator.register_tool_executor("read_file", self._mock_read_file)
        self.orchestrator.register_tool_executor("write_to_file", self._mock_write_file)
        self.orchestrator.register_tool_executor("list_files", self._mock_list_files)
        self.orchestrator.register_tool_executor("search_files", self._mock_search_files)
        
    async def _mock_read_file(self, path: str, **kwargs):
        """Mock read file operation"""
        await asyncio.sleep(0.1)
        if path == "script.py":
            return {"success": True, "content": "if __name__ == '__main__':\n    print('Hello')"}
        return {"success": True, "content": f"Content of {path}"}
        
    async def _mock_write_file(self, path: str, content: str, **kwargs):
        """Mock write file operation"""
        await asyncio.sleep(0.1)
        return {"success": True, "path": path}
        
    async def _mock_list_files(self, path: str = ".", **kwargs):
        """Mock list files operation"""
        await asyncio.sleep(0.1)
        return {"success": True, "files": ["file1.py", "file2.py", "script.py"]}
        
    async def _mock_search_files(self, path: str, regex: str, **kwargs):
        """Mock search files operation"""
        await asyncio.sleep(0.1)
        return {
            "success": True, 
            "matches": [
                {"file": "file1.py", "line": 1, "content": "import os"},
                {"file": "file2.py", "line": 3, "content": "def test():"}
            ]
        }
        
    async def test_enhanced_multi_step_execution(self):
        """Test enhanced multi-step execution with dynamic planning"""
        user_input = "read all Python files in the project"
        
        result = await self.orchestrator.analyze_and_execute(user_input)
        
        assert result["success"]
        assert result["is_multi_step"]
        
        # Should have executed list_files and multiple read_file operations
        executed_steps = result.get("executed_steps", [])
        assert len(executed_steps) > 1
        
    async def test_error_recovery(self):
        """Test error recovery functionality"""
        # Register a failing tool
        async def failing_read_file(path: str, **kwargs):
            if path == "missing.py":
                return {"success": False, "error": "File not found"}
            return {"success": True, "content": "test content"}
            
        self.orchestrator.register_tool_executor("read_file", failing_read_file)
        
        plan = ExecutionPlan()
        step1 = ExecutionStep("step1", "read_file", {"path": "missing.py"}, set())
        step2 = ExecutionStep("step2", "read_file", {"path": "existing.py"}, set())
        plan.add_step(step1)
        plan.add_step(step2)
        
        result = await self.orchestrator.execute_plan(plan)
        
        # Should continue with other steps despite failure
        assert len(result["executed_steps"]) >= 1
        assert len(result["failed_steps"]) >= 1
        
    async def test_progress_reporting_enhancement(self):
        """Test enhanced progress reporting"""
        plan = ExecutionPlan()
        for i in range(3):
            step = ExecutionStep(f"step{i}", "read_file", {"path": f"file{i}.py"}, set())
            plan.add_step(step)
            
        result = await self.orchestrator.execute_plan(plan)
        
        progress_summary = self.orchestrator.progress_reporter.get_progress_summary()
        
        assert "success_rate" in progress_summary
        assert "active_tools" in progress_summary
        assert "is_complete" in progress_summary
        assert progress_summary["completed_steps"] == 3


class TestEnhancedParser:
    """Test enhanced parser functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.parser = ResponseParser()
        
    def test_tool_calls_with_context_extraction(self):
        """Test extraction of tool calls with context"""
        response = """I'll help you create the files. First, let me read the existing structure:

<read_file>
<path>main.py</path>
</read_file>

Then I'll create the new file:

<write_to_file>
<path>helper.py</path>
<content>def helper_function():
    return "Hello"</content>
</write_to_file>"""
        
        content_blocks = self.parser.parse_complete(response)
        tool_calls_with_context = self.parser.extract_tool_calls_with_context(content_blocks)
        
        assert len(tool_calls_with_context) == 2
        
        # First tool call should have context about reading structure
        first_call = tool_calls_with_context[0]
        assert "structure" in first_call["preceding_text"].lower()
        
        # Second tool call should have context about creating file
        second_call = tool_calls_with_context[1]
        assert "create" in second_call["preceding_text"].lower()
        
    def test_dependency_extraction_from_context(self):
        """Test extraction of dependencies from context"""
        preceding_text = "After reading the configuration file"
        following_text = "then we can proceed with the setup"
        
        dependencies = self.parser._extract_dependencies_from_context(preceding_text, following_text)
        
        assert len(dependencies) > 0
        assert any("after" in dep for dep in dependencies)
        assert any("then" in dep for dep in dependencies)


# Test runner for manual execution
if __name__ == "__main__":
    import unittest
    
    class TestRunner:
        def run_all_tests(self):
            """Run all tests manually"""
            print("Running Enhanced Orchestration Tests...")
            
            # Test enhanced request analyzer
            test_analyzer = TestEnhancedRequestAnalyzer()
            try:
                test_analyzer.setup_method()
                test_analyzer.test_enhanced_multi_step_detection()
                test_analyzer.test_multiple_action_detection()
                test_analyzer.test_implicit_multi_step_detection()
                test_analyzer.test_project_wide_operation_detection()
                print("✅ Enhanced request analyzer tests passed")
            except Exception as e:
                print(f"❌ Enhanced request analyzer tests failed: {e}")
                
            # Test dynamic planner
            test_planner = TestDynamicPlanner()
            try:
                test_planner.setup_method()
                test_planner.test_result_handler_registration()
                test_planner.test_modification_application()
                print("✅ Dynamic planner tests passed")
            except Exception as e:
                print(f"❌ Dynamic planner tests failed: {e}")
                
            # Test enhanced parser
            test_parser = TestEnhancedParser()
            try:
                test_parser.setup_method()
                test_parser.test_tool_calls_with_context_extraction()
                test_parser.test_dependency_extraction_from_context()
                print("✅ Enhanced parser tests passed")
            except Exception as e:
                print(f"❌ Enhanced parser tests failed: {e}")
                
            print("Enhanced tests completed!")
    
    runner = TestRunner()
    runner.run_all_tests()
