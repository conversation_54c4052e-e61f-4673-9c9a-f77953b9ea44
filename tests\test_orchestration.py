"""
Tests for Multi-Step Tool Execution Orchestration

This module tests:
- Tool orchestration and execution planning
- Multi-step execution with dependencies
- Error handling and retry logic
- Progress reporting and state management
- Batch operation optimization
"""

import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import pytest

# Import the modules to test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.orchestration import (
    ToolOrchestrator, ExecutionPlan, ExecutionStep, ExecutionMode,
    ExecutionState, ExecutionStatus, RequestAnalyzer, ErrorHandler, ProgressReporter
)


class TestExecutionPlan:
    """Test execution plan functionality"""
    
    def test_plan_creation(self):
        """Test basic plan creation"""
        plan = ExecutionPlan(name="Test Plan")
        
        assert plan.name == "Test Plan"
        assert len(plan) == 0
        assert plan.plan_id is not None
        
    def test_step_addition(self):
        """Test adding steps to plan"""
        plan = ExecutionPlan()
        
        step = ExecutionStep(
            step_id="step1",
            tool_name="read_file",
            params={"path": "test.py"},
            dependencies=set()
        )
        
        step_id = plan.add_step(step)
        assert step_id == "step1"
        assert len(plan) == 1
        assert "step1" in plan
        
    def test_dependency_management(self):
        """Test dependency management"""
        plan = ExecutionPlan()
        
        step1 = ExecutionStep("step1", "list_files", {"path": "."}, set())
        step2 = ExecutionStep("step2", "read_file", {"path": "test.py"}, set())
        
        plan.add_step(step1)
        plan.add_step(step2)
        plan.add_dependency("step2", "step1")
        
        assert "step1" in step2.dependencies
        
    def test_plan_validation(self):
        """Test plan validation"""
        plan = ExecutionPlan()
        
        step1 = ExecutionStep("step1", "read_file", {"path": "test.py"}, set())
        step2 = ExecutionStep("step2", "write_to_file", {"path": "out.py", "content": "test"}, {"step1"})
        
        plan.add_step(step1)
        plan.add_step(step2)
        
        is_valid, errors = plan.validate_plan()
        assert is_valid
        assert len(errors) == 0
        
    def test_circular_dependency_detection(self):
        """Test circular dependency detection"""
        plan = ExecutionPlan()
        
        step1 = ExecutionStep("step1", "read_file", {"path": "test.py"}, {"step2"})
        step2 = ExecutionStep("step2", "write_to_file", {"path": "out.py", "content": "test"}, {"step1"})
        
        plan.add_step(step1)
        plan.add_step(step2)
        
        is_valid, errors = plan.validate_plan()
        assert not is_valid
        assert len(errors) > 0
        
    def test_parallel_groups(self):
        """Test parallel group generation"""
        plan = ExecutionPlan()
        
        # Independent steps that can run in parallel
        step1 = ExecutionStep("step1", "read_file", {"path": "file1.py"}, set(), mode=ExecutionMode.PARALLEL)
        step2 = ExecutionStep("step2", "read_file", {"path": "file2.py"}, set(), mode=ExecutionMode.PARALLEL)
        step3 = ExecutionStep("step3", "write_to_file", {"path": "out.py", "content": "test"}, {"step1", "step2"})
        
        plan.add_step(step1)
        plan.add_step(step2)
        plan.add_step(step3)
        
        groups = plan.get_parallel_groups()
        
        # First group should have step1 and step2 (parallel)
        # Second group should have step3 (depends on first group)
        assert len(groups) >= 2
        assert len(groups[0]) == 2  # step1 and step2
        assert len(groups[1]) == 1  # step3


class TestExecutionState:
    """Test execution state management"""
    
    def test_state_creation(self):
        """Test execution state creation"""
        state = ExecutionState("exec1", "read_file", {"path": "test.py"})
        
        assert state.execution_id == "exec1"
        assert state.tool_name == "read_file"
        assert state.status == ExecutionStatus.PENDING
        assert state.result is None
        
    def test_state_transitions(self):
        """Test state transitions"""
        state = ExecutionState("exec1", "read_file", {"path": "test.py"})
        
        # Start execution
        state.start_execution()
        assert state.status == ExecutionStatus.EXECUTING
        assert state.metrics.start_time is not None
        
        # Complete execution
        from core.orchestration.execution_state import ExecutionResult
        result = ExecutionResult(success=True, data={"content": "test"})
        state.complete_execution(result)
        
        assert state.status == ExecutionStatus.COMPLETED
        assert state.result.success is True
        assert state.metrics.end_time is not None
        assert state.metrics.duration is not None
        
    def test_dependency_tracking(self):
        """Test dependency tracking"""
        state = ExecutionState("exec1", "read_file", {"path": "test.py"})
        
        state.add_dependency("dep1")
        state.add_dependent("dep2")
        
        assert "dep1" in state.dependencies
        assert "dep2" in state.dependents
        
        # Test readiness
        assert not state.is_ready_to_execute(set())
        assert state.is_ready_to_execute({"dep1"})


class TestRequestAnalyzer:
    """Test request analysis functionality"""
    
    def test_single_operation_detection(self):
        """Test detection of single operations"""
        analyzer = RequestAnalyzer()
        
        result = analyzer.analyze_request("read the file main.py")
        
        assert not result["is_multi_step"]
        assert "read_file" in result["suggested_tools"]
        
    def test_multi_step_detection(self):
        """Test detection of multi-step operations"""
        analyzer = RequestAnalyzer()
        
        result = analyzer.analyze_request("check all project files and analyze them")
        
        assert result["is_multi_step"]
        assert result["confidence"] > 0.7
        
    def test_project_analysis_pattern(self):
        """Test project analysis pattern recognition"""
        analyzer = RequestAnalyzer()
        
        result = analyzer.analyze_request("analyze the entire project workspace")
        
        assert result["is_multi_step"]
        assert "list_files" in result["suggested_tools"]
        assert "read_file" in result["suggested_tools"]
        
    def test_execution_plan_creation(self):
        """Test execution plan creation from analysis"""
        analyzer = RequestAnalyzer()
        
        analysis = analyzer.analyze_request("read all files in the project")
        plan = analyzer.create_execution_plan("read all files in the project", analysis)
        
        assert plan is not None
        assert len(plan) > 0
        assert any(step.tool_name == "list_files" for step in plan)


class TestErrorHandler:
    """Test error handling and retry logic"""
    
    def test_error_classification(self):
        """Test error classification"""
        handler = ErrorHandler()
        
        # Test different error types
        assert handler.classify_error("Permission denied") == handler.error_patterns["permission denied"]
        assert handler.classify_error("File not found") == handler.error_patterns["file not found"]
        assert handler.classify_error("Connection timeout") == handler.error_patterns["timeout"]
        
    def test_retry_logic(self):
        """Test retry logic"""
        handler = ErrorHandler()
        
        # Mock operation that fails twice then succeeds
        call_count = 0
        async def mock_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return {"success": True}
            
        # Test retry execution
        result = asyncio.run(handler.execute_with_retry(
            "test_exec", "test_tool", mock_operation
        ))
        
        assert result["success"] is True
        assert call_count == 3
        
    def test_retry_config(self):
        """Test retry configuration"""
        from core.orchestration.error_handler import RetryConfig, RetryStrategy
        
        config = RetryConfig(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            max_attempts=5,
            base_delay=0.1
        )
        
        handler = ErrorHandler(config)
        
        # Test delay calculation
        delay1 = handler.calculate_delay(1, config)
        delay2 = handler.calculate_delay(2, config)
        delay3 = handler.calculate_delay(3, config)
        
        assert delay2 > delay1
        assert delay3 > delay2


class TestProgressReporter:
    """Test progress reporting functionality"""
    
    def test_progress_tracking(self):
        """Test progress tracking"""
        reporter = ProgressReporter()
        
        # Start plan
        reporter.start_plan(3, estimated_duration=10.0)
        
        # Start executions
        reporter.start_execution("exec1", "read_file")
        reporter.start_execution("exec2", "write_file")
        
        # Update progress
        reporter.update_execution_progress("exec1", 50.0, "Reading file...")
        reporter.complete_execution("exec1", success=True)
        
        # Check progress
        summary = reporter.get_progress_summary()
        assert summary["completed_steps"] == 1
        assert summary["total_steps"] == 3
        assert summary["overall_progress"] > 0
        
    def test_event_handling(self):
        """Test progress event handling"""
        reporter = ProgressReporter()
        events = []
        
        def event_handler(event):
            events.append(event)
            
        reporter.add_event_handler(event_handler)
        
        reporter.start_plan(1)
        reporter.start_execution("exec1", "test_tool")
        reporter.complete_execution("exec1", success=True)
        
        assert len(events) >= 3  # plan_start, step_start, step_complete


class TestToolOrchestrator:
    """Test the main orchestrator functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.orchestrator = ToolOrchestrator()
        
        # Register mock tool executors
        self.orchestrator.register_tool_executor("read_file", self._mock_read_file)
        self.orchestrator.register_tool_executor("write_to_file", self._mock_write_file)
        self.orchestrator.register_tool_executor("list_files", self._mock_list_files)
        
    async def _mock_read_file(self, path: str, **kwargs):
        """Mock read file operation"""
        await asyncio.sleep(0.1)  # Simulate work
        return {"success": True, "content": f"Content of {path}"}
        
    async def _mock_write_file(self, path: str, content: str, **kwargs):
        """Mock write file operation"""
        await asyncio.sleep(0.1)  # Simulate work
        return {"success": True, "path": path}
        
    async def _mock_list_files(self, path: str = ".", **kwargs):
        """Mock list files operation"""
        await asyncio.sleep(0.1)  # Simulate work
        return {"success": True, "files": ["file1.py", "file2.py"]}
        
    async def test_single_tool_execution(self):
        """Test single tool execution"""
        # Create a simple plan
        plan = ExecutionPlan()
        step = ExecutionStep("step1", "read_file", {"path": "test.py"}, set())
        plan.add_step(step)
        
        result = await self.orchestrator.execute_plan(plan)
        
        assert result["success"] is True
        assert len(result["executed_steps"]) == 1
        assert len(result["failed_steps"]) == 0
        
    async def test_multi_step_execution(self):
        """Test multi-step execution with dependencies"""
        plan = ExecutionPlan()
        
        step1 = ExecutionStep("step1", "list_files", {"path": "."}, set())
        step2 = ExecutionStep("step2", "read_file", {"path": "file1.py"}, {"step1"})
        
        plan.add_step(step1)
        plan.add_step(step2)
        
        result = await self.orchestrator.execute_plan(plan)
        
        assert result["success"] is True
        assert len(result["executed_steps"]) == 2
        assert "step1" in result["executed_steps"]
        assert "step2" in result["executed_steps"]
        
    async def test_parallel_execution(self):
        """Test parallel execution"""
        plan = ExecutionPlan()
        
        step1 = ExecutionStep("step1", "read_file", {"path": "file1.py"}, set(), mode=ExecutionMode.PARALLEL)
        step2 = ExecutionStep("step2", "read_file", {"path": "file2.py"}, set(), mode=ExecutionMode.PARALLEL)
        
        plan.add_step(step1)
        plan.add_step(step2)
        
        result = await self.orchestrator.execute_plan(plan)
        
        assert result["success"] is True
        assert len(result["executed_steps"]) == 2
        
    async def test_error_handling(self):
        """Test error handling in orchestration"""
        # Register a failing tool
        async def failing_tool(**kwargs):
            raise Exception("Tool failure")
            
        self.orchestrator.register_tool_executor("failing_tool", failing_tool)
        
        plan = ExecutionPlan()
        step = ExecutionStep("step1", "failing_tool", {}, set())
        plan.add_step(step)
        
        result = await self.orchestrator.execute_plan(plan)
        
        assert result["success"] is False
        assert len(result["failed_steps"]) == 1
        assert "step1" in result["failed_steps"]


# Test runner
if __name__ == "__main__":
    # Run tests manually if pytest is not available
    import unittest
    
    class TestRunner:
        def run_all_tests(self):
            """Run all tests manually"""
            print("Running Orchestration Tests...")
            
            # Test execution plan
            test_plan = TestExecutionPlan()
            try:
                test_plan.test_plan_creation()
                test_plan.test_step_addition()
                test_plan.test_dependency_management()
                test_plan.test_plan_validation()
                print("✅ Execution plan tests passed")
            except Exception as e:
                print(f"❌ Execution plan tests failed: {e}")
                
            # Test execution state
            test_state = TestExecutionState()
            try:
                test_state.test_state_creation()
                test_state.test_state_transitions()
                test_state.test_dependency_tracking()
                print("✅ Execution state tests passed")
            except Exception as e:
                print(f"❌ Execution state tests failed: {e}")
                
            # Test request analyzer
            test_analyzer = TestRequestAnalyzer()
            try:
                test_analyzer.test_single_operation_detection()
                test_analyzer.test_multi_step_detection()
                test_analyzer.test_project_analysis_pattern()
                print("✅ Request analyzer tests passed")
            except Exception as e:
                print(f"❌ Request analyzer tests failed: {e}")
                
            print("Tests completed!")
    
    runner = TestRunner()
    runner.run_all_tests()
