"""
Tests for Response Parser

This module tests the XML-based response parsing functionality
including tool call extraction and content separation.
"""

import pytest
from core.parser import <PERSON><PERSON><PERSON><PERSON>, ToolUse, TextContent


class TestResponseParser:
    """Test cases for ResponseParser"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.parser = ResponseParser()
    
    def test_simple_text_parsing(self):
        """Test parsing simple text without tool calls"""
        message = "This is a simple text message without any tool calls."
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 1
        assert isinstance(content_blocks[0], TextContent)
        assert content_blocks[0].content == message
        assert not content_blocks[0].partial
    
    def test_simple_tool_call_parsing(self):
        """Test parsing a simple tool call"""
        message = """<read_file>
<path>test.py</path>
</read_file>"""
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 1
        assert isinstance(content_blocks[0], ToolUse)
        assert content_blocks[0].name == "read_file"
        assert content_blocks[0].params["path"] == "test.py"
        assert not content_blocks[0].partial
    
    def test_mixed_content_parsing(self):
        """Test parsing mixed text and tool calls"""
        message = """I'll help you read the file.

<read_file>
<path>example.py</path>
</read_file>

The file has been read successfully!"""
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 3
        
        # First block: text
        assert isinstance(content_blocks[0], TextContent)
        assert "I'll help you read the file." in content_blocks[0].content
        
        # Second block: tool call
        assert isinstance(content_blocks[1], ToolUse)
        assert content_blocks[1].name == "read_file"
        assert content_blocks[1].params["path"] == "example.py"
        
        # Third block: text
        assert isinstance(content_blocks[2], TextContent)
        assert "The file has been read successfully!" in content_blocks[2].content
    
    def test_write_file_with_content(self):
        """Test parsing write_to_file with content parameter"""
        message = """<write_to_file>
<path>hello.py</path>
<content>print("Hello, World!")
print("This is a test")
</content>
</write_to_file>"""
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 1
        assert isinstance(content_blocks[0], ToolUse)
        assert content_blocks[0].name == "write_to_file"
        assert content_blocks[0].params["path"] == "hello.py"
        
        expected_content = 'print("Hello, World!")\nprint("This is a test")'
        assert content_blocks[0].params["content"] == expected_content
    
    def test_nested_content_handling(self):
        """Test handling of nested content in write_to_file"""
        message = """<write_to_file>
<path>test.html</path>
<content><html>
<head><title>Test</title></head>
<body>
<p>This content has nested tags</p>
</body>
</html></content>
</write_to_file>"""
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 1
        assert isinstance(content_blocks[0], ToolUse)
        assert content_blocks[0].name == "write_to_file"
        
        content = content_blocks[0].params["content"]
        assert "<html>" in content
        assert "<title>Test</title>" in content
        assert "</html>" in content
    
    def test_replace_in_file_parsing(self):
        """Test parsing replace_in_file with diff parameter"""
        message = """<replace_in_file>
<path>example.py</path>
<diff>------- SEARCH
def old_function():
    pass
=======
def new_function():
    return "updated"
+++++++ REPLACE</diff>
</replace_in_file>"""
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 1
        assert isinstance(content_blocks[0], ToolUse)
        assert content_blocks[0].name == "replace_in_file"
        assert content_blocks[0].params["path"] == "example.py"
        assert "------- SEARCH" in content_blocks[0].params["diff"]
        assert "+++++++ REPLACE" in content_blocks[0].params["diff"]
    
    def test_multiple_tool_calls(self):
        """Test parsing multiple tool calls in sequence"""
        message = """First, I'll read the file:

<read_file>
<path>input.txt</path>
</read_file>

Now I'll create a new file:

<write_to_file>
<path>output.txt</path>
<content>Processed content</content>
</write_to_file>

Done!"""
        
        content_blocks = self.parser.parse_complete(message)
        
        assert len(content_blocks) == 5
        
        # Check sequence: text, tool, text, tool, text
        assert isinstance(content_blocks[0], TextContent)
        assert isinstance(content_blocks[1], ToolUse)
        assert isinstance(content_blocks[2], TextContent)
        assert isinstance(content_blocks[3], ToolUse)
        assert isinstance(content_blocks[4], TextContent)
        
        # Check tool calls
        assert content_blocks[1].name == "read_file"
        assert content_blocks[3].name == "write_to_file"
    
    def test_partial_parsing(self):
        """Test parsing partial/streaming content"""
        partial_message = """I'll create a file for you.

<write_to_file>
<path>test.py</path>
<content>def hello():
    print("Hello"""
        
        content_blocks = self.parser.parse_streaming(partial_message)
        
        # Should have text and partial tool call
        assert len(content_blocks) >= 1
        
        # Find the tool use block
        tool_block = None
        for block in content_blocks:
            if isinstance(block, ToolUse):
                tool_block = block
                break
        
        assert tool_block is not None
        assert tool_block.partial
        assert tool_block.name == "write_to_file"
    
    def test_tool_validation(self):
        """Test tool call validation"""
        # Valid tool call
        valid_tool = ToolUse(
            name="read_file",
            params={"path": "test.py"},
            partial=False
        )
        assert self.parser.validate_tool_call(valid_tool)
        
        # Invalid tool call - missing required parameter
        invalid_tool = ToolUse(
            name="read_file",
            params={},
            partial=False
        )
        assert not self.parser.validate_tool_call(invalid_tool)
        
        # Invalid tool call - unknown tool
        unknown_tool = ToolUse(
            name="unknown_tool",
            params={"param": "value"},
            partial=False
        )
        assert not self.parser.validate_tool_call(unknown_tool)
    
    def test_extract_tool_calls(self):
        """Test extracting only tool calls from content blocks"""
        message = """Text before

<read_file>
<path>file1.py</path>
</read_file>

More text

<write_to_file>
<path>file2.py</path>
<content>content</content>
</write_to_file>

Text after"""
        
        content_blocks = self.parser.parse_complete(message)
        tool_calls = self.parser.extract_tool_calls(content_blocks)
        
        assert len(tool_calls) == 2
        assert all(isinstance(tool, ToolUse) for tool in tool_calls)
        assert tool_calls[0].name == "read_file"
        assert tool_calls[1].name == "write_to_file"
    
    def test_extract_text_content(self):
        """Test extracting only text content from content blocks"""
        message = """First paragraph.

<read_file>
<path>test.py</path>
</read_file>

Second paragraph.

<write_to_file>
<path>output.py</path>
<content>code</content>
</write_to_file>

Third paragraph."""
        
        content_blocks = self.parser.parse_complete(message)
        text_content = self.parser.extract_text_content(content_blocks)
        
        assert "First paragraph." in text_content
        assert "Second paragraph." in text_content
        assert "Third paragraph." in text_content
        # Tool calls should not be in text content
        assert "<read_file>" not in text_content
        assert "<write_to_file>" not in text_content


if __name__ == "__main__":
    pytest.main([__file__])
