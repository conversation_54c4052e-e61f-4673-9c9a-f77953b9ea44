"""
Tests for Temporary Workspace Functionality

This module tests:
- Temp workspace creation and configuration
- Directory creation and management
- Multi-file operations
- Workspace session tracking
- File relationship analysis
"""

import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import pytest

# Import the modules to test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import PyIDE
from core.workspace_session import WorkspaceSession
from core.tools.file_ops import FileOperations
from core.security.ignore import IgnoreController
from core.security.validator import SecurityValidator


class TestTempWorkspace:
    """Test temporary workspace functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.config = {
            "workspace": {
                "default_directory": str(self.test_dir),
                "temp_workspace": {
                    "enabled": True,
                    "directory_name": "temp",
                    "use_session_subdirectories": False,
                    "auto_create": True,
                    "preserve_on_exit": True
                }
            },
            "security": {
                "max_file_size": 1024 * 1024,
                "workspace_only": True,
                "require_approval": False,
                "ignore_file": ".pyideignore",
                "allowed_extensions": [".py", ".txt", ".md", ".json"],
                "blocked_paths": ["__pycache__", ".git"]
            },
            "logging": {
                "level": "INFO"
            }
        }
        
    def teardown_method(self):
        """Cleanup test environment"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            
    def test_temp_workspace_creation(self):
        """Test that temp workspace is created correctly"""
        pyide = PyIDE()
        pyide.config = self.config
        pyide._setup_workspace()
        
        # Check that temp workspace was created
        temp_path = self.test_dir / "temp"
        assert temp_path.exists()
        assert temp_path.is_dir()
        
        # Check that workspace_path points to temp directory
        assert pyide.workspace_path == temp_path
        assert pyide.base_workspace_path == self.test_dir
        
    def test_temp_workspace_disabled(self):
        """Test behavior when temp workspace is disabled"""
        self.config["workspace"]["temp_workspace"]["enabled"] = False
        
        pyide = PyIDE()
        pyide.config = self.config
        pyide._setup_workspace()
        
        # Check that workspace_path points to base directory
        assert pyide.workspace_path == self.test_dir
        assert pyide.base_workspace_path == self.test_dir
        
    def test_ignore_file_creation(self):
        """Test that default .pyideignore file is created"""
        pyide = PyIDE()
        pyide.config = self.config
        pyide._setup_workspace()
        
        ignore_file = pyide.workspace_path / ".pyideignore"
        assert ignore_file.exists()
        
        # Check content
        content = ignore_file.read_text()
        assert "__pycache__/" in content
        assert "*.pyc" in content
        assert ".venv/" in content


class TestWorkspaceSession:
    """Test workspace session management"""
    
    def setup_method(self):
        """Setup test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.session = WorkspaceSession(self.test_dir)
        
    def teardown_method(self):
        """Cleanup test environment"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            
    def test_session_initialization(self):
        """Test session initialization"""
        assert self.session.workspace_path == self.test_dir
        assert self.session.session_id.startswith("session_")
        assert len(self.session.created_files) == 0
        assert len(self.session.modified_files) == 0
        
    def test_file_tracking(self):
        """Test file creation and modification tracking"""
        # Create a test file
        test_file = self.test_dir / "test.py"
        test_file.write_text("print('hello')")
        
        # Track file creation
        self.session.track_file_created(str(test_file))
        assert "test.py" in self.session.created_files
        
        # Track file modification
        test_file.write_text("print('hello world')")
        self.session.track_file_modified(str(test_file))
        # Should still be in created_files, not moved to modified
        assert "test.py" in self.session.created_files
        assert "test.py" not in self.session.modified_files
        
        # Track another file modification
        test_file2 = self.test_dir / "test2.py"
        test_file2.write_text("print('test2')")
        self.session.track_file_modified(str(test_file2))
        assert "test2.py" in self.session.modified_files
        
    def test_session_persistence(self):
        """Test session data persistence"""
        # Track some files
        test_file = self.test_dir / "test.py"
        test_file.write_text("print('hello')")
        self.session.track_file_created(str(test_file))
        
        # Create new session with same workspace
        new_session = WorkspaceSession(self.test_dir)
        
        # Should load existing session data
        assert "test.py" in new_session.created_files
        
    def test_session_summary(self):
        """Test session summary generation"""
        # Track some files
        test_file = self.test_dir / "test.py"
        test_file.write_text("print('hello')")
        self.session.track_file_created(str(test_file))
        
        summary = self.session.get_session_summary()
        assert summary["files_created"] == 1
        assert summary["files_modified"] == 0
        assert "test.py" in summary["created_files"]


class TestFileOperations:
    """Test file operations with temp workspace"""
    
    def setup_method(self):
        """Setup test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.temp_dir = self.test_dir / "temp"
        self.temp_dir.mkdir()
        
        self.config = {
            "security": {
                "max_file_size": 1024 * 1024,
                "workspace_only": True,
                "require_approval": False,
                "allowed_extensions": [".py", ".txt", ".md", ".json"],
                "blocked_paths": ["__pycache__"]
            }
        }
        
        # Create components
        self.ignore_controller = IgnoreController(str(self.temp_dir))
        self.security_validator = SecurityValidator(self.config)
        self.workspace_session = WorkspaceSession(self.temp_dir)
        
        self.file_ops = FileOperations(
            self.config,
            self.ignore_controller,
            self.security_validator,
            workspace_path=self.temp_dir,
            workspace_session=self.workspace_session
        )
        
    def teardown_method(self):
        """Cleanup test environment"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            
    async def test_directory_creation(self):
        """Test directory creation functionality"""
        result = await self.file_ops.create_directory("test_dir", require_approval=False)
        
        assert result["success"] is True
        assert (self.temp_dir / "test_dir").exists()
        assert "test_dir" in self.workspace_session.created_files
        
    async def test_file_creation_tracking(self):
        """Test file creation with session tracking"""
        result = await self.file_ops.write_file("test.py", "print('hello')", require_approval=False)
        
        assert result["success"] is True
        assert (self.temp_dir / "test.py").exists()
        assert "test.py" in self.workspace_session.created_files
        
    async def test_multi_file_operations(self):
        """Test operations across multiple files"""
        # Create multiple files
        files = ["main.py", "utils.py", "config.json", "README.md"]
        contents = [
            "from utils import helper\nprint('main')",
            "def helper():\n    return 'help'",
            '{"name": "test", "version": "1.0"}',
            "# Test Project\nThis is a test."
        ]
        
        for file_path, content in zip(files, contents):
            result = await self.file_ops.write_file(file_path, content, require_approval=False)
            assert result["success"] is True
            
        # Check all files are tracked
        tracked_files = self.workspace_session.get_all_tracked_files()
        for file_path in files:
            assert file_path in tracked_files
            
    async def test_workspace_info(self):
        """Test workspace information retrieval"""
        # Create some files and directories
        await self.file_ops.create_directory("src", require_approval=False)
        await self.file_ops.write_file("src/main.py", "print('hello')", require_approval=False)
        await self.file_ops.write_file("README.md", "# Test", require_approval=False)
        
        workspace_info = await self.file_ops.get_workspace_info()
        assert workspace_info["success"] is True
        assert workspace_info["total_files"] >= 2
        assert workspace_info["total_directories"] >= 1
        
    async def test_file_relationships(self):
        """Test file relationship analysis"""
        # Create a Python file with imports
        python_content = """
import os
from pathlib import Path
from utils import helper

def main():
    print("Hello from main")
"""
        await self.file_ops.write_file("main.py", python_content, require_approval=False)
        
        relationships = await self.file_ops.get_file_relationships("main.py")
        assert relationships["success"] is True
        assert "os" in relationships["imports"]
        assert "pathlib" in relationships["imports"]
        assert "utils" in relationships["imports"]


# Test runner
if __name__ == "__main__":
    # Run tests manually if pytest is not available
    import unittest
    
    class TestRunner:
        def run_all_tests(self):
            """Run all tests manually"""
            print("Running Temp Workspace Tests...")
            
            # Test temp workspace creation
            test_workspace = TestTempWorkspace()
            test_workspace.setup_method()
            try:
                test_workspace.test_temp_workspace_creation()
                print("✅ Temp workspace creation test passed")
            except Exception as e:
                print(f"❌ Temp workspace creation test failed: {e}")
            finally:
                test_workspace.teardown_method()
                
            # Test session management
            test_session = TestWorkspaceSession()
            test_session.setup_method()
            try:
                test_session.test_session_initialization()
                test_session.test_file_tracking()
                print("✅ Session management tests passed")
            except Exception as e:
                print(f"❌ Session management tests failed: {e}")
            finally:
                test_session.teardown_method()
                
            print("Tests completed!")
    
    runner = TestRunner()
    runner.run_all_tests()
